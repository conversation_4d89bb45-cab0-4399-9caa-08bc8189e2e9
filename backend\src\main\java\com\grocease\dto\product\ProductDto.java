package com.grocease.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDto {
    private String id;
    private String name;
    private String description;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private BigDecimal discount;
    private String image;
    private String category;
    private String unit;
    private Boolean inStock;
    private BigDecimal rating;
    private Integer reviewCount;
    private List<String> tags;
}
