package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.user.AddressDto;
import com.grocease.dto.user.CreateAddressRequest;
import com.grocease.dto.user.UserDto;
import com.grocease.entity.User;
import com.grocease.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;

    @GetMapping("/profile")
    public ResponseEntity<ApiResponse<UserDto>> getUserProfile(@AuthenticationPrincipal User user) {
        log.info("Getting profile for user: {}", user.getId());
        UserDto userDto = userService.getUserById(user.getId());
        return ResponseEntity.ok(ApiResponse.success(userDto, "User profile retrieved successfully"));
    }

    @PutMapping("/profile")
    public ResponseEntity<ApiResponse<UserDto>> updateUserProfile(
            @AuthenticationPrincipal User user,
            @Valid @RequestBody UserDto userDto) {
        log.info("Updating profile for user: {}", user.getId());
        UserDto updatedUser = userService.updateUser(user.getId(), userDto);
        return ResponseEntity.ok(ApiResponse.success(updatedUser, "User profile updated successfully"));
    }

    @GetMapping("/addresses")
    public ResponseEntity<ApiResponse<List<AddressDto>>> getUserAddresses(@AuthenticationPrincipal User user) {
        log.info("Getting addresses for user: {}", user.getId());
        List<AddressDto> addresses = userService.getUserAddresses(user.getId());
        return ResponseEntity.ok(ApiResponse.success(addresses, "User addresses retrieved successfully"));
    }

    @PostMapping("/addresses")
    public ResponseEntity<ApiResponse<AddressDto>> createAddress(
            @AuthenticationPrincipal User user,
            @Valid @RequestBody CreateAddressRequest request) {
        log.info("Creating address for user: {}", user.getId());
        AddressDto address = userService.createAddress(user.getId(), request);
        return ResponseEntity.ok(ApiResponse.success(address, "Address created successfully"));
    }

    @PutMapping("/addresses/{addressId}")
    public ResponseEntity<ApiResponse<AddressDto>> updateAddress(
            @AuthenticationPrincipal User user,
            @PathVariable Long addressId,
            @Valid @RequestBody CreateAddressRequest request) {
        log.info("Updating address {} for user: {}", addressId, user.getId());
        AddressDto address = userService.updateAddress(user.getId(), addressId, request);
        return ResponseEntity.ok(ApiResponse.success(address, "Address updated successfully"));
    }

    @DeleteMapping("/addresses/{addressId}")
    public ResponseEntity<ApiResponse<String>> deleteAddress(
            @AuthenticationPrincipal User user,
            @PathVariable Long addressId) {
        log.info("Deleting address {} for user: {}", addressId, user.getId());
        userService.deleteAddress(user.getId(), addressId);
        return ResponseEntity.ok(ApiResponse.success("Address deleted successfully", "Address has been removed"));
    }
}
