package com.grocease.dto.user;

import com.grocease.entity.Address;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressDto {
    private String id;
    private Address.AddressType type;
    private String street;
    private String city;
    private String state;
    private String zipCode;
    private Boolean isDefault;
}
