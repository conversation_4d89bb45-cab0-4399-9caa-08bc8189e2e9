# =============================================================================
# GROCEASE BACKEND - PRODUCTION READY CONFIGURATION
# =============================================================================
# This is the main configuration file that works across all environments
# Environment-specific settings are controlled via environment variables
# =============================================================================

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api
  error:
    include-message: always
    include-binding-errors: always

# Spring Configuration
spring:
  application:
    name: grocease-backend

  # Profile Configuration
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}

  # Database Configuration
  datasource:
    url: ${DATABASE_URL:********************************************}
    username: ${DB_USERNAME:grocease_user}
    password: ${DB_PASSWORD:grocease_password}
    driver-class-name: ${DB_DRIVER:org.postgresql.Driver}
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:20}
      minimum-idle: ${DB_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: ${JPA_DIALECT:org.hibernate.dialect.PostgreSQLDialect}
        format_sql: ${JPA_FORMAT_SQL:false}
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    defer-datasource-initialization: true

  # SQL Initialization
  sql:
    init:
      mode: ${SQL_INIT_MODE:never}
      data-locations: ${SQL_DATA_LOCATIONS:classpath:data.sql}

  # Mail Configuration
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:10MB}
      max-request-size: ${MAX_REQUEST_SIZE:10MB}

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:}
  expiration: ${JWT_EXPIRATION:********} # 24 hours in milliseconds
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:*********} # 7 days in milliseconds

# Cloudinary Configuration
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:}
  api-key: ${CLOUDINARY_API_KEY:}
  api-secret: ${CLOUDINARY_API_SECRET:}

# Firebase Configuration
firebase:
  config:
    file: ${FIREBASE_CONFIG_FILE:firebase-service-account.json}

# Application Configuration
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:19006}
  upload:
    max-file-size: ${MAX_FILE_SIZE:10MB}
    max-request-size: ${MAX_REQUEST_SIZE:10MB}
  security:
    jwt:
      header: ${JWT_HEADER:Authorization}
      prefix: ${JWT_PREFIX:Bearer }

# Logging Configuration
logging:
  level:
    com.grocease: ${LOG_LEVEL_APP:INFO}
    org.springframework.security: ${LOG_LEVEL_SECURITY:WARN}
    org.hibernate.SQL: ${LOG_LEVEL_SQL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOG_LEVEL_SQL_PARAMS:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/grocease-backend.log}

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS:health,info,metrics}
      base-path: /actuator
  endpoint:
    health:
      show-details: ${HEALTH_SHOW_DETAILS:when-authorized}
  info:
    env:
      enabled: true
