package com.grocease.repository;

import com.grocease.entity.OtpToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface OtpTokenRepository extends JpaRepository<OtpToken, Long> {

    Optional<OtpToken> findByTokenAndTypeAndIsUsedFalse(String token, OtpToken.OtpType type);

    @Modifying
    @Query("UPDATE OtpToken o SET o.isUsed = true WHERE o.user.id = :userId AND o.type = :type AND o.isUsed = false")
    void markAllAsUsedForUserAndType(@Param("userId") Long userId, @Param("type") OtpToken.OtpType type);

    @Modifying
    @Query("DELETE FROM OtpToken o WHERE o.expiresAt < :now")
    void deleteExpiredTokens(@Param("now") LocalDateTime now);

    boolean existsByUserIdAndTypeAndIsUsedFalseAndExpiresAtAfter(Long userId, OtpToken.OtpType type, LocalDateTime now);
}
