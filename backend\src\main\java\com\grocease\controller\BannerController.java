package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.banner.BannerDto;
import com.grocease.service.BannerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/banners")
@RequiredArgsConstructor
@Slf4j
public class BannerController {

    private final BannerService bannerService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<BannerDto>>> getAllBanners() {
        log.info("Getting all active banners");
        List<BannerDto> banners = bannerService.getAllActiveBanners();
        return ResponseEntity.ok(ApiResponse.success(banners, "Banners retrieved successfully"));
    }
}
