package com.grocease.dto.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategorySalesDto {
    private Long categoryId;
    private String categoryName;
    private String categoryImage;
    private BigDecimal totalRevenue;
    private Long orderCount;
    private Long totalItemsSold;
    private Long uniqueProductsSold;
    private BigDecimal averageOrderValue;
}
