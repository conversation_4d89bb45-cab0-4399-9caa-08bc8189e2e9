package com.grocease.dto.notification;

import com.grocease.entity.NotificationHistory;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendNotificationRequest {
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotBlank(message = "Message is required")
    private String message;
    
    @NotNull(message = "Type is required")
    private NotificationHistory.NotificationType type;
    
    private List<Long> userIds; // If null, send to all users
    
    private Map<String, String> data; // Additional data for the notification
    
    private Boolean isBroadcast = false;
}
