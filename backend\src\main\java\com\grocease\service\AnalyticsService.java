package com.grocease.service;

import com.grocease.dto.analytics.*;
import com.grocease.entity.Order;
import com.grocease.repository.AnalyticsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnalyticsService {

    private final AnalyticsRepository analyticsRepository;

    public List<SalesDataDto> getMonthlySalesData(int months) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minus(months, ChronoUnit.MONTHS);
        
        log.info("Getting monthly sales data from {} to {}", startDate, endDate);
        return analyticsRepository.getMonthlySalesData(startDate, endDate);
    }

    public List<SalesDataDto> getWeeklySalesData(int weeks) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minus(weeks, ChronoUnit.WEEKS);
        
        log.info("Getting weekly sales data from {} to {}", startDate, endDate);
        return analyticsRepository.getWeeklySalesData(startDate, endDate);
    }

    public List<ProductSalesDto> getMostSoldProducts(int days, int limit) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minus(days, ChronoUnit.DAYS);
        Pageable pageable = PageRequest.of(0, limit);
        
        log.info("Getting most sold products from {} to {}, limit: {}", startDate, endDate, limit);
        return analyticsRepository.getMostSoldProducts(startDate, endDate, pageable);
    }

    public List<CategorySalesDto> getPopularCategories(int days) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minus(days, ChronoUnit.DAYS);
        
        log.info("Getting popular categories from {} to {}", startDate, endDate);
        return analyticsRepository.getPopularCategories(startDate, endDate);
    }

    public List<UserEngagementDto> getUserEngagementMetrics(int days) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minus(days, ChronoUnit.DAYS);
        
        log.info("Getting user engagement metrics from {} to {}", startDate, endDate);
        
        // For simplicity, we'll get monthly engagement data
        List<UserEngagementDto> engagementData = List.of(
            analyticsRepository.getUserEngagementData(startDate, endDate, endDate, "monthly")
        );
        
        // Calculate retention rate
        engagementData.forEach(data -> {
            if (data.getActiveUsers() > 0 && data.getReturningUsers() > 0) {
                double retentionRate = (data.getReturningUsers().doubleValue() / data.getActiveUsers().doubleValue()) * 100;
                data.setRetentionRate(Math.round(retentionRate * 100.0) / 100.0);
            }
        });
        
        return engagementData;
    }

    public DashboardOverviewDto getDashboardOverview() {
        log.info("Getting dashboard overview data");
        
        BigDecimal todayRevenue = analyticsRepository.getTodayRevenue();
        BigDecimal monthlyRevenue = analyticsRepository.getMonthlyRevenue();
        Long todayOrders = analyticsRepository.getTodayOrdersCount();
        Long monthlyOrders = analyticsRepository.getMonthlyOrdersCount();
        Long totalUsers = analyticsRepository.getTotalUsersCount();
        Long activeUsers = analyticsRepository.getActiveUsersCount(LocalDateTime.now().minus(30, ChronoUnit.DAYS));
        BigDecimal averageOrderValue = analyticsRepository.getAverageOrderValue();
        
        // Get order status distribution for last 30 days
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minus(30, ChronoUnit.DAYS);
        List<Object[]> statusDistributionData = analyticsRepository.getOrderStatusDistribution(thirtyDaysAgo);
        Map<String, Long> orderStatusDistribution = new HashMap<>();
        
        for (Object[] row : statusDistributionData) {
            Order.OrderStatus status = (Order.OrderStatus) row[0];
            Long count = (Long) row[1];
            orderStatusDistribution.put(status.name(), count);
        }
        
        // Calculate growth percentages (simplified - comparing with previous month)
        LocalDateTime lastMonthStart = LocalDateTime.now().minus(2, ChronoUnit.MONTHS);
        LocalDateTime lastMonthEnd = LocalDateTime.now().minus(1, ChronoUnit.MONTHS);
        
        BigDecimal lastMonthRevenue = getRevenueForPeriod(lastMonthStart, lastMonthEnd);
        Long lastMonthOrders = getOrdersCountForPeriod(lastMonthStart, lastMonthEnd);
        
        BigDecimal revenueGrowthPercentage = calculateGrowthPercentage(lastMonthRevenue, monthlyRevenue);
        Long orderGrowthPercentage = calculateGrowthPercentage(lastMonthOrders, monthlyOrders);
        
        return DashboardOverviewDto.builder()
                .todayRevenue(todayRevenue)
                .monthlyRevenue(monthlyRevenue)
                .todayOrders(todayOrders)
                .monthlyOrders(monthlyOrders)
                .totalUsers(totalUsers)
                .activeUsers(activeUsers)
                .averageOrderValue(averageOrderValue)
                .orderStatusDistribution(orderStatusDistribution)
                .revenueGrowthPercentage(revenueGrowthPercentage)
                .orderGrowthPercentage(orderGrowthPercentage)
                .build();
    }

    private BigDecimal getRevenueForPeriod(LocalDateTime startDate, LocalDateTime endDate) {
        // This would need a custom query, for now return a placeholder
        return BigDecimal.ZERO;
    }

    private Long getOrdersCountForPeriod(LocalDateTime startDate, LocalDateTime endDate) {
        // This would need a custom query, for now return a placeholder
        return 0L;
    }

    private BigDecimal calculateGrowthPercentage(BigDecimal oldValue, BigDecimal newValue) {
        if (oldValue.compareTo(BigDecimal.ZERO) == 0) {
            return newValue.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.valueOf(100) : BigDecimal.ZERO;
        }
        
        BigDecimal growth = newValue.subtract(oldValue)
                .divide(oldValue, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        
        return growth.setScale(2, RoundingMode.HALF_UP);
    }

    private Long calculateGrowthPercentage(Long oldValue, Long newValue) {
        if (oldValue == 0) {
            return newValue > 0 ? 100L : 0L;
        }
        
        return Math.round(((newValue - oldValue) * 100.0) / oldValue);
    }
}
