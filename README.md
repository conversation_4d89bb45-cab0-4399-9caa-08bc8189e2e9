# GroceEase - Complete Mobile Grocery Shopping App

A fully functional mobile grocery shopping app with **complete authentication system**, **order management**, and **secure JWT storage** built with React Native, TypeScript, NativeWind, and TanStack Query.

## ✨ Key Features

### 🔐 Authentication System
- **Complete Auth Flow** - Login, Register, Forgot Password, OTP Verification
- **JWT Security** - Secure token storage with Keychain/Keystore
- **Persistent Login** - Auto-login on app restart with token refresh
- **Demo Credentials** - `<EMAIL>` / `password123`

### 🛒 Shopping Experience
- **Product Catalog** - Categories, search, filtering, and sorting
- **Shopping Cart** - Add/remove items with quantity controls
- **Checkout Flow** - Complete order placement with address management
- **Real-time Search** - Debounced product search functionality

### 📦 Order Management
- **Order History** - View all orders with status filtering
- **Order Tracking** - Visual progress indicators for order status
- **Reorder Feature** - Quickly add previous order items to cart

### 🎨 Design & UX
- **Mobile-first Design** - Optimized for mobile devices
- **Green/Orange Theme** - Psychologically appealing grocery colors
- **Smooth Animations** - Polished user interactions
- **Location Services** - GPS-based location detection

## 🛠 Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Styling**: NativeWind (Tailwind CSS)
- **State Management**: TanStack Query + Zustand + React Context
- **Navigation**: React Navigation v6
- **Storage**: AsyncStorage + Keychain/Keystore
- **Services**: Expo Location, Notifications, Vector Icons

## 📁 Project Structure

```
src/
├── components/         # Reusable UI components
├── screens/           # Screen components
│   ├── auth/          # Authentication screens (6 screens)
│   └── ...            # Shopping & order screens (10+ screens)
├── navigation/        # Navigation setup
├── services/          # API & storage services
├── hooks/             # Custom React hooks
├── types/             # TypeScript definitions
└── utils/             # Utility functions
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16+)
- Expo CLI
- Expo Go app (for device testing)

### Installation & Running
```bash
# Install dependencies
npm install

# Start development server
npm start

# Run on specific platform
npm run android  # Android emulator
npm run ios      # iOS simulator
npm run web      # Web browser
```

### Testing the App
1. **Authentication**: Use `<EMAIL>` / `password123`
2. **Shopping**: Browse products, add to cart, checkout
3. **Orders**: View order history from Profile screen
4. **Features**: Test location, notifications, search

## 📱 App Screens & Flow

### 🔐 Authentication (6 Screens)
- **Login** → **Register** → **Email Verification**
- **Forgot Password** → **OTP Verification** → **Reset Password**

### 🛒 Shopping (10+ Screens)
- **Onboarding** → **Home** → **Categories** → **Product List**
- **Product Details** → **Cart** → **Checkout** → **Order Confirmation**
- **Search** → **Profile** → **Settings**

### 📦 Order Management (2 Screens)
- **Order History** → **Order Details** (with reorder functionality)

## 🔧 Configuration & API

### Mock API Integration
All data is served through mock APIs with realistic delays:
```typescript
// Easy to replace with real APIs
export const api = {
  getProducts: async () => {
    // Replace with: const response = await fetch('/api/products');
    await delay(1000);
    return { data: mockProducts };
  }
};
```

### Order Status System
- **Pending** → **Confirmed** → **Preparing** → **Out for Delivery** → **Delivered**
- **Cancelled** (can happen at any stage)

## 🎨 Styling & Theme

### NativeWind Configuration
- **Tailwind CSS** for React Native with custom color palette
- **Green/Orange Theme** - Psychologically appealing for grocery shopping
- **Responsive Design** - Mobile-first approach with consistent spacing

### Color Palette
- **Primary Green**: `#22c55e` (freshness, organic)
- **Accent Orange**: `#f59e0b` (discounts, promotions)
- **Neutral Grays**: Clean backgrounds and readable text

## 🔄 Production Ready

### Security Features
- ✅ **JWT Authentication** with secure keychain storage
- ✅ **Form Validation** and input sanitization
- ✅ **Secure Logout** with complete data cleanup

### Performance Optimizations
- ✅ **TanStack Query** for efficient data fetching and caching
- ✅ **Optimized Navigation** with React Navigation
- ✅ **Memory Management** and leak prevention

### Easy API Integration
Replace mock functions in `src/services/api.ts` with real endpoints:
```typescript
// Current: Mock API with delays
export const api = {
  getProducts: async () => {
    await delay(1000);
    return { data: mockProducts };
  }
};

// Replace with: Real API calls
export const api = {
  getProducts: async () => {
    const response = await fetch('/api/products');
    return response.json();
  }
};
```

## � Project Stats
- **15+ Screens** - Complete app functionality
- **6 Auth Screens** - Full authentication system
- **20+ Components** - Reusable UI components
- **100% TypeScript** - Type-safe development
- **Production Ready** - Secure and optimized

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Submit a pull request

## 📄 License
MIT License - feel free to use for personal or commercial projects.
