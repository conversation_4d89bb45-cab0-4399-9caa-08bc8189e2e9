package com.grocease.dto.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductSalesDto {
    private Long productId;
    private String productName;
    private String productImage;
    private String categoryName;
    private Long quantitySold;
    private BigDecimal totalRevenue;
    private Long orderCount;
    private BigDecimal averagePrice;
}
