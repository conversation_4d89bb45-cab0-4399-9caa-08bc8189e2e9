# =============================================================================
# LOCAL PROFILE CONFIGURATION
# =============================================================================
# This configuration is used for local development with PostgreSQL
# Activate with: --spring.profiles.active=local (default)
# =============================================================================

spring:
  # Local PostgreSQL Database Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  # SQL Initialization for Local Development
  sql:
    init:
      mode: never # Set to 'always' if you want to run data.sql

# Local Development Logging Configuration
logging:
  level:
    com.grocease: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Local Development Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: always
