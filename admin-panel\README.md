# GrocEase Admin Panel

A modern, responsive admin panel built with Next.js 14, TypeScript, and shadcn/ui for managing the GrocEase grocery delivery platform.

![Next.js](https://img.shields.io/badge/Next.js-14-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3-38B2AC)
![shadcn/ui](https://img.shields.io/badge/shadcn%2Fui-Latest-000000)

## 🚀 Features

### 📊 **Dashboard & Analytics**
- **Real-time Metrics**: Revenue, orders, users, and performance KPIs
- **Interactive Charts**: Sales trends, order status distribution, category performance
- **Time-based Analysis**: Weekly and monthly sales data visualization
- **Growth Tracking**: Revenue and order growth percentages

### 🛒 **Order Management**
- **Order Listing**: Comprehensive order table with search and filtering
- **Status Updates**: Real-time order status management with validation
- **Order Details**: Detailed order view with customer and item information
- **Status Tracking**: Complete order lifecycle management

### 📦 **Product Management**
- **Product Catalog**: Full CRUD operations for products
- **Category Management**: Organize products by categories
- **Image Upload**: Cloudinary integration for product images
- **Inventory Tracking**: Stock management and availability

### 👥 **User Management**
- **User Listing**: View and manage customer accounts
- **User Details**: Customer profiles and order history
- **Account Status**: User verification and account management

### 🔔 **Notification System**
- **Push Notifications**: Send targeted notifications to users
- **Notification History**: Track delivery status and engagement
- **Broadcast Messages**: Send announcements to all users
- **Automated Triggers**: Order status and promotional notifications

### 🎯 **Marketing Tools**
- **Banner Management**: Create and manage promotional banners
- **Campaign Analytics**: Track marketing performance
- **User Engagement**: Monitor user activity and retention

### 🔐 **Security & Authentication**
- **JWT Authentication**: Secure admin login with token management
- **Role-based Access**: Admin-only access control
- **Protected Routes**: Automatic authentication checks
- **Session Management**: Persistent login with refresh tokens

## 🛠️ Tech Stack

| Technology | Purpose | Version |
|------------|---------|---------|
| **Next.js** | React framework with SSR/SSG | 14.0.4 |
| **TypeScript** | Type-safe JavaScript | 5.x |
| **Tailwind CSS** | Utility-first CSS framework | 3.x |
| **shadcn/ui** | Modern UI component library | Latest |
| **Radix UI** | Headless UI primitives | Latest |
| **TanStack Query** | Data fetching and caching | 5.x |
| **Zustand** | State management | 4.x |
| **Recharts** | Chart and data visualization | 2.x |
| **Lucide React** | Beautiful icons | Latest |
| **Axios** | HTTP client | 1.x |

## 📋 Prerequisites

- **Node.js** 18.x or higher
- **npm** or **yarn** package manager
- **GrocEase Backend** running on port 8080

## 🚀 Quick Start

### 1. **Clone and Install**

```bash
# Navigate to admin panel directory
cd admin-panel

# Install dependencies
npm install
# or
yarn install
```

### 2. **Environment Configuration**

Create a `.env.local` file:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8080/api

# Optional: Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
```

### 3. **Development Server**

```bash
# Start development server
npm run dev
# or
yarn dev
```

The admin panel will be available at `http://localhost:3000`

### 4. **Login Credentials**

Use these demo credentials to access the admin panel:

```
Email: <EMAIL>
Password: admin123
```

## 📁 Project Structure

```
admin-panel/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   │   ├── dashboard/          # Dashboard pages
│   │   ├── orders/             # Order management
│   │   ├── products/           # Product management
│   │   ├── analytics/          # Analytics pages
│   │   ├── notifications/      # Notification system
│   │   └── login/              # Authentication
│   ├── components/             # Reusable components
│   │   ├── ui/                 # shadcn/ui components
│   │   ├── layout/             # Layout components
│   │   ├── dashboard/          # Dashboard-specific components
│   │   └── auth/               # Authentication components
│   ├── lib/                    # Utilities and configurations
│   │   ├── api.ts              # API client
│   │   └── utils.ts            # Helper functions
│   ├── store/                  # State management
│   │   └── auth.ts             # Authentication store
│   └── types/                  # TypeScript type definitions
├── public/                     # Static assets
└── package.json               # Dependencies and scripts
```

## 🎨 UI Components

The admin panel uses **shadcn/ui** components for a consistent, modern design:

- **Cards**: Dashboard metrics and content containers
- **Tables**: Data display with sorting and pagination
- **Forms**: Input handling with validation
- **Charts**: Data visualization with Recharts
- **Modals**: Overlay dialogs for actions
- **Navigation**: Sidebar and header navigation
- **Buttons**: Various button styles and states
- **Badges**: Status indicators and labels

## 📊 Dashboard Features

### **Key Metrics Cards**
- Today's revenue and orders
- Monthly performance
- User statistics
- Average order value

### **Interactive Charts**
- Sales trend line charts
- Order status pie charts
- Category performance bars
- User engagement metrics

### **Real-time Updates**
- Live data refresh
- Automatic chart updates
- Status change notifications

## 🔧 Development

### **Available Scripts**

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

### **Code Style**

- **ESLint**: Code linting and formatting
- **TypeScript**: Strict type checking
- **Prettier**: Code formatting (recommended)
- **Tailwind CSS**: Utility-first styling

### **Component Development**

```typescript
// Example component structure
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Component Title</CardTitle>
      </CardHeader>
      <CardContent>
        <Button>Action</Button>
      </CardContent>
    </Card>
  )
}
```

## 🚀 Production Deployment

### **Build and Deploy**

```bash
# Build for production
npm run build

# Start production server
npm run start
```

### **Environment Variables**

```bash
# Production API URL
NEXT_PUBLIC_API_URL=https://api.grocease.com

# Optional production configs
NEXT_PUBLIC_ANALYTICS_ID=prod-analytics-id
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
```

### **Deployment Platforms**

- **Vercel**: Recommended for Next.js apps
- **Netlify**: Static site deployment
- **AWS Amplify**: Full-stack deployment
- **Docker**: Containerized deployment

## 🔒 Security

- **Authentication**: JWT-based admin authentication
- **Route Protection**: Automatic redirect for unauthenticated users
- **API Security**: Token-based API requests
- **Input Validation**: Form validation and sanitization
- **HTTPS**: Secure communication in production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Add tests if applicable
5. Commit changes: `git commit -m 'Add new feature'`
6. Push to branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **shadcn/ui** - Beautiful and accessible UI components
- **Radix UI** - Unstyled, accessible UI primitives
- **Tailwind CSS** - Utility-first CSS framework
- **Next.js Team** - Amazing React framework
- **Vercel** - Deployment and hosting platform

---

**Built with ❤️ for the GrocEase platform**
