# 🚀 GrocEase Backend - Environment Setup Guide

This guide will help you set up environment variables for running the GrocEase backend locally and in production.

## 📋 Quick Start

### 1. Copy Environment Template
```bash
cp .env.example .env
```

### 2. Edit Environment Variables
Open `.env` file and fill in your actual values:

```bash
# Required for basic functionality
SPRING_PROFILES_ACTIVE=local
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
JWT_SECRET=your_super_secret_jwt_key_at_least_32_characters_long
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### 3. Run the Application
```bash
mvn spring-boot:run
```

## 🔧 Detailed Configuration

### 🗄️ Database Setup

#### PostgreSQL Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (using Homebrew)
brew install postgresql
brew services start postgresql

# Windows
# Download from: https://www.postgresql.org/download/windows/
```

#### Create Database and User
```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create database
CREATE DATABASE grocease_db;

-- Create user
CREATE USER grocease_user WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE grocease_db TO grocease_user;

-- Exit
\q
```

#### Environment Variables
```bash
DATABASE_URL=********************************************
DB_USERNAME=grocease_user
DB_PASSWORD=your_secure_password
```

### 🔐 JWT Configuration

Generate a secure JWT secret (at least 32 characters):
```bash
# Generate random secret
openssl rand -base64 32
```

```bash
JWT_SECRET=your_generated_secret_here
JWT_EXPIRATION=********      # 24 hours
JWT_REFRESH_EXPIRATION=*********  # 7 days
```

### 📧 Email Configuration

#### Gmail Setup
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"

```bash
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-character-app-password
```

#### Other Email Providers
```bash
# Outlook
MAIL_HOST=smtp-mail.outlook.com
MAIL_PORT=587

# Yahoo
MAIL_HOST=smtp.mail.yahoo.com
MAIL_PORT=587
```

### ☁️ Cloudinary Setup

1. Sign up at [Cloudinary](https://cloudinary.com/)
2. Get your credentials from Dashboard

```bash
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### 🔥 Firebase Setup (Optional)

For push notifications:
1. Create Firebase project
2. Generate service account key
3. Download JSON file as `firebase-service-account.json`
4. Place in `src/main/resources/`

```bash
FIREBASE_CONFIG_FILE=firebase-service-account.json
```

## 🌍 Environment Profiles

### Local Development (default)
```bash
SPRING_PROFILES_ACTIVE=local
```
- Uses PostgreSQL
- Debug logging enabled
- All management endpoints exposed

### Development with H2
```bash
SPRING_PROFILES_ACTIVE=dev
```
- Uses H2 in-memory database
- H2 console available at `/api/h2-console`
- Verbose logging

### Production
```bash
SPRING_PROFILES_ACTIVE=prod
```
- Uses PostgreSQL
- Minimal logging
- Restricted management endpoints
- Performance optimizations

## 🔒 Security Best Practices

### Environment Variables Security
- Never commit `.env` file to version control
- Use strong, unique passwords
- Rotate secrets regularly
- Use different secrets for different environments

### Production Considerations
- Use environment-specific databases
- Enable SSL/TLS
- Use secure JWT secrets (256-bit minimum)
- Implement proper backup strategies
- Monitor application logs

## 🐳 Docker Environment

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=*******************************************
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=grocease_db
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔍 Troubleshooting

### Common Issues

#### Database Connection Failed
- Check PostgreSQL is running
- Verify database exists
- Check username/password
- Ensure database URL is correct

#### JWT Token Issues
- Ensure JWT_SECRET is at least 32 characters
- Check token expiration settings

#### Email Not Sending
- Verify Gmail app password (not regular password)
- Check firewall/network restrictions
- Ensure 2FA is enabled for Gmail

#### File Upload Issues
- Check Cloudinary credentials
- Verify file size limits
- Check network connectivity

### Logs and Debugging
```bash
# View application logs
tail -f logs/grocease-backend.log

# Check specific log levels
grep "ERROR" logs/grocease-backend.log
```

## 📞 Support

If you encounter issues:
1. Check the logs first
2. Verify all required environment variables are set
3. Ensure external services (database, email, Cloudinary) are accessible
4. Check firewall and network settings
