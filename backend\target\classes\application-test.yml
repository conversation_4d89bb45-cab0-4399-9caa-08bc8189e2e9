# Minimal Test Configuration
server:
  port: 8080

spring:
  application:
    name: grocease-backend
  
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect
  
  h2:
    console:
      enabled: true

# JWT Configuration (minimal)
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000
  refresh-expiration: 604800000

# Disable other services for testing
cloudinary:
  cloud-name: test
  api-key: test
  api-secret: test

firebase:
  config:
    file: not-required-for-testing

logging:
  level:
    com.grocease: DEBUG
