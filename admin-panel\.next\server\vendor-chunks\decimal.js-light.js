"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decimal.js-light";
exports.ids = ["vendor-chunks/decimal.js-light"];
exports.modules = {

/***/ "(ssr)/./node_modules/decimal.js-light/decimal.mjs":
/*!***************************************************!*\
  !*** ./node_modules/decimal.js-light/decimal.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decimal: () => (/* binding */ Decimal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  decimal.js-light v2.5.1\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js-light\r\n *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n *  MIT Expat Licence\r\n */ // ------------------------------------  EDITABLE DEFAULTS  ------------------------------------- //\n// The limit on the value of `precision`, and on the value of the first argument to\n// `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\nvar MAX_DIGITS = 1e9, // The initial configuration properties of the Decimal constructor.\ndefaults = {\n    // These values must be integers within the stated ranges (inclusive).\n    // Most of these values can be changed during run-time using `Decimal.config`.\n    // The maximum number of significant digits of the result of a calculation or base conversion.\n    // E.g. `Decimal.config({ precision: 20 });`\n    precision: 20,\n    // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\n    // `toFixed`, `toPrecision` and `toSignificantDigits`.\n    //\n    // ROUND_UP         0 Away from zero.\n    // ROUND_DOWN       1 Towards zero.\n    // ROUND_CEIL       2 Towards +Infinity.\n    // ROUND_FLOOR      3 Towards -Infinity.\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n    //\n    // E.g.\n    // `Decimal.rounding = 4;`\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n    rounding: 4,\n    // The exponent value at and beneath which `toString` returns exponential notation.\n    // JavaScript numbers: -7\n    toExpNeg: -7,\n    // The exponent value at and above which `toString` returns exponential notation.\n    // JavaScript numbers: 21\n    toExpPos: 21,\n    // The natural logarithm of 10.\n    // 115 digits\n    LN10: \"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286\"\n}, // ------------------------------------ END OF EDITABLE DEFAULTS -------------------------------- //\nDecimal, external = true, decimalError = \"[DecimalError] \", invalidArgument = decimalError + \"Invalid argument: \", exponentOutOfRange = decimalError + \"Exponent out of range: \", mathfloor = Math.floor, mathpow = Math.pow, isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i, ONE, BASE = 1e7, LOG_BASE = 7, MAX_SAFE_INTEGER = 9007199254740991, MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE), // Decimal.prototype object\nP = {};\n// Decimal prototype methods\n/*\r\n *  absoluteValue                       abs\r\n *  comparedTo                          cmp\r\n *  decimalPlaces                       dp\r\n *  dividedBy                           div\r\n *  dividedToIntegerBy                  idiv\r\n *  equals                              eq\r\n *  exponent\r\n *  greaterThan                         gt\r\n *  greaterThanOrEqualTo                gte\r\n *  isInteger                           isint\r\n *  isNegative                          isneg\r\n *  isPositive                          ispos\r\n *  isZero\r\n *  lessThan                            lt\r\n *  lessThanOrEqualTo                   lte\r\n *  logarithm                           log\r\n *  minus                               sub\r\n *  modulo                              mod\r\n *  naturalExponential                  exp\r\n *  naturalLogarithm                    ln\r\n *  negated                             neg\r\n *  plus                                add\r\n *  precision                           sd\r\n *  squareRoot                          sqrt\r\n *  times                               mul\r\n *  toDecimalPlaces                     todp\r\n *  toExponential\r\n *  toFixed\r\n *  toInteger                           toint\r\n *  toNumber\r\n *  toPower                             pow\r\n *  toPrecision\r\n *  toSignificantDigits                 tosd\r\n *  toString\r\n *  valueOf                             val\r\n */ /*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */ P.absoluteValue = P.abs = function() {\n    var x = new this.constructor(this);\n    if (x.s) x.s = 1;\n    return x;\n};\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value\r\n *\r\n */ P.comparedTo = P.cmp = function(y) {\n    var i, j, xdL, ydL, x = this;\n    y = new x.constructor(y);\n    // Signs differ?\n    if (x.s !== y.s) return x.s || -y.s;\n    // Compare exponents.\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\n    xdL = x.d.length;\n    ydL = y.d.length;\n    // Compare digit by digit.\n    for(i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i){\n        if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\n    }\n    // Compare lengths.\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\n};\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */ P.decimalPlaces = P.dp = function() {\n    var x = this, w = x.d.length - 1, dp = (w - x.e) * LOG_BASE;\n    // Subtract the number of trailing zeros of the last word.\n    w = x.d[w];\n    if (w) for(; w % 10 == 0; w /= 10)dp--;\n    return dp < 0 ? 0 : dp;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.dividedBy = P.div = function(y) {\n    return divide(this, new this.constructor(y));\n};\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, truncated to `precision` significant digits.\r\n *\r\n */ P.dividedToIntegerBy = P.idiv = function(y) {\n    var x = this, Ctor = x.constructor;\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\n};\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */ P.equals = P.eq = function(y) {\n    return !this.cmp(y);\n};\n/*\r\n * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n *\r\n */ P.exponent = function() {\n    return getBase10Exponent(this);\n};\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */ P.greaterThan = P.gt = function(y) {\n    return this.cmp(y) > 0;\n};\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */ P.greaterThanOrEqualTo = P.gte = function(y) {\n    return this.cmp(y) >= 0;\n};\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */ P.isInteger = P.isint = function() {\n    return this.e > this.d.length - 2;\n};\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */ P.isNegative = P.isneg = function() {\n    return this.s < 0;\n};\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */ P.isPositive = P.ispos = function() {\n    return this.s > 0;\n};\n/*\r\n * Return true if the value of this Decimal is 0, otherwise return false.\r\n *\r\n */ P.isZero = function() {\n    return this.s === 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */ P.lessThan = P.lt = function(y) {\n    return this.cmp(y) < 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */ P.lessThanOrEqualTo = P.lte = function(y) {\n    return this.cmp(y) < 1;\n};\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n * `precision` significant digits.\r\n *\r\n * If no base is specified, return log[10](x).\r\n *\r\n * log[base](x) = ln(x) / ln(base)\r\n *\r\n * The maximum error of the result is 1 ulp (unit in the last place).\r\n *\r\n * [base] {number|string|Decimal} The base of the logarithm.\r\n *\r\n */ P.logarithm = P.log = function(base) {\n    var r, x = this, Ctor = x.constructor, pr = Ctor.precision, wpr = pr + 5;\n    // Default base is 10.\n    if (base === void 0) {\n        base = new Ctor(10);\n    } else {\n        base = new Ctor(base);\n        // log[-b](x) = NaN\n        // log[0](x)  = NaN\n        // log[1](x)  = NaN\n        if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + \"NaN\");\n    }\n    // log[b](-x) = NaN\n    // log[b](0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // log[b](1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    external = false;\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.minus = P.sub = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.modulo = P.mod = function(y) {\n    var q, x = this, Ctor = x.constructor, pr = Ctor.precision;\n    y = new Ctor(y);\n    // x % 0 = NaN\n    if (!y.s) throw Error(decimalError + \"NaN\");\n    // Return x if x is 0.\n    if (!x.s) return round(new Ctor(x), pr);\n    // Prevent rounding of intermediate calculations.\n    external = false;\n    q = divide(x, y, 0, 1).times(y);\n    external = true;\n    return x.minus(q);\n};\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.naturalExponential = P.exp = function() {\n    return exp(this);\n};\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * truncated to `precision` significant digits.\r\n *\r\n */ P.naturalLogarithm = P.ln = function() {\n    return ln(this);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */ P.negated = P.neg = function() {\n    var x = new this.constructor(this);\n    x.s = -x.s || 0;\n    return x;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.plus = P.add = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\n};\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */ P.precision = P.sd = function(z) {\n    var e, sd, w, x = this;\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n    e = getBase10Exponent(x) + 1;\n    w = x.d.length - 1;\n    sd = w * LOG_BASE + 1;\n    w = x.d[w];\n    // If non-zero...\n    if (w) {\n        // Subtract the number of trailing zeros of the last word.\n        for(; w % 10 == 0; w /= 10)sd--;\n        // Add the number of digits of the first word.\n        for(w = x.d[0]; w >= 10; w /= 10)sd++;\n    }\n    return z && e > sd ? e : sd;\n};\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.squareRoot = P.sqrt = function() {\n    var e, n, pr, r, s, t, wpr, x = this, Ctor = x.constructor;\n    // Negative or zero?\n    if (x.s < 1) {\n        if (!x.s) return new Ctor(0);\n        // sqrt(-x) = NaN\n        throw Error(decimalError + \"NaN\");\n    }\n    e = getBase10Exponent(x);\n    external = false;\n    // Initial estimate.\n    s = Math.sqrt(+x);\n    // Math.sqrt underflow/overflow?\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n    if (s == 0 || s == 1 / 0) {\n        n = digitsToString(x.d);\n        if ((n.length + e) % 2 == 0) n += \"0\";\n        s = Math.sqrt(n);\n        e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n        if (s == 1 / 0) {\n            n = \"5e\" + e;\n        } else {\n            n = s.toExponential();\n            n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n        }\n        r = new Ctor(n);\n    } else {\n        r = new Ctor(s.toString());\n    }\n    pr = Ctor.precision;\n    s = wpr = pr + 3;\n    // Newton-Raphson iteration.\n    for(;;){\n        t = r;\n        r = t.plus(divide(x, t, wpr + 2)).times(0.5);\n        if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\n            n = n.slice(wpr - 3, wpr + 1);\n            // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n            // 4999, i.e. approaching a rounding boundary, continue the iteration.\n            if (s == wpr && n == \"4999\") {\n                // On the first iteration only, check to see if rounding up gives the exact result as the\n                // nines may infinitely repeat.\n                round(t, pr + 1, 0);\n                if (t.times(t).eq(x)) {\n                    r = t;\n                    break;\n                }\n            } else if (n != \"9999\") {\n                break;\n            }\n            wpr += 4;\n        }\n    }\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.times = P.mul = function(y) {\n    var carry, e, i, k, r, rL, t, xdL, ydL, x = this, Ctor = x.constructor, xd = x.d, yd = (y = new Ctor(y)).d;\n    // Return 0 if either is 0.\n    if (!x.s || !y.s) return new Ctor(0);\n    y.s *= x.s;\n    e = x.e + y.e;\n    xdL = xd.length;\n    ydL = yd.length;\n    // Ensure xd points to the longer array.\n    if (xdL < ydL) {\n        r = xd;\n        xd = yd;\n        yd = r;\n        rL = xdL;\n        xdL = ydL;\n        ydL = rL;\n    }\n    // Initialise the result array with zeros.\n    r = [];\n    rL = xdL + ydL;\n    for(i = rL; i--;)r.push(0);\n    // Multiply!\n    for(i = ydL; --i >= 0;){\n        carry = 0;\n        for(k = xdL + i; k > i;){\n            t = r[k] + yd[i] * xd[k - i - 1] + carry;\n            r[k--] = t % BASE | 0;\n            carry = t / BASE | 0;\n        }\n        r[k] = (r[k] + carry) % BASE | 0;\n    }\n    // Remove trailing zeros.\n    for(; !r[--rL];)r.pop();\n    if (carry) ++e;\n    else r.shift();\n    y.d = r;\n    y.e = e;\n    return external ? round(y, Ctor.precision) : y;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toDecimalPlaces = P.todp = function(dp, rm) {\n    var x = this, Ctor = x.constructor;\n    x = new Ctor(x);\n    if (dp === void 0) return x;\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toExponential = function(dp, rm) {\n    var str, x = this, Ctor = x.constructor;\n    if (dp === void 0) {\n        str = toString(x, true);\n    } else {\n        checkInt32(dp, 0, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), dp + 1, rm);\n        str = toString(x, true, dp + 1);\n    }\n    return str;\n};\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */ P.toFixed = function(dp, rm) {\n    var str, y, x = this, Ctor = x.constructor;\n    if (dp === void 0) return toString(x);\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\n    // To determine whether to add the minus sign look at the value before it was rounded,\n    // i.e. look at `x` rather than `y`.\n    return x.isneg() && !x.isZero() ? \"-\" + str : str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */ P.toInteger = P.toint = function() {\n    var x = this, Ctor = x.constructor;\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\n};\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n *\r\n */ P.toNumber = function() {\n    return +this;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n * truncated to `precision` significant digits.\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * The maximum error is 1 ulp (unit in last place).\r\n *\r\n * y {number|string|Decimal} The power to which to raise this Decimal.\r\n *\r\n */ P.toPower = P.pow = function(y) {\n    var e, k, pr, r, sign, yIsInt, x = this, Ctor = x.constructor, guard = 12, yn = +(y = new Ctor(y));\n    // pow(x, 0) = 1\n    if (!y.s) return new Ctor(ONE);\n    x = new Ctor(x);\n    // pow(0, y > 0) = 0\n    // pow(0, y < 0) = Infinity\n    if (!x.s) {\n        if (y.s < 1) throw Error(decimalError + \"Infinity\");\n        return x;\n    }\n    // pow(1, y) = 1\n    if (x.eq(ONE)) return x;\n    pr = Ctor.precision;\n    // pow(x, 1) = x\n    if (y.eq(ONE)) return round(x, pr);\n    e = y.e;\n    k = y.d.length - 1;\n    yIsInt = e >= k;\n    sign = x.s;\n    if (!yIsInt) {\n        // pow(x < 0, y non-integer) = NaN\n        if (sign < 0) throw Error(decimalError + \"NaN\");\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n        r = new Ctor(ONE);\n        // Max k of 9007199254740991 takes 53 loop iterations.\n        // Maximum digits array length; leaves [28, 34] guard digits.\n        e = Math.ceil(pr / LOG_BASE + 4);\n        external = false;\n        for(;;){\n            if (k % 2) {\n                r = r.times(x);\n                truncate(r.d, e);\n            }\n            k = mathfloor(k / 2);\n            if (k === 0) break;\n            x = x.times(x);\n            truncate(x.d, e);\n        }\n        external = true;\n        return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\n    }\n    // Result is negative if x is negative and the last digit of integer y is odd.\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\n    x.s = 1;\n    external = false;\n    r = y.times(ln(x, pr + guard));\n    external = true;\n    r = exp(r);\n    r.s = sign;\n    return r;\n};\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toPrecision = function(sd, rm) {\n    var e, str, x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        e = getBase10Exponent(x);\n        str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), sd, rm);\n        e = getBase10Exponent(x);\n        str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\n    }\n    return str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toSignificantDigits = P.tosd = function(sd, rm) {\n    var x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        sd = Ctor.precision;\n        rm = Ctor.rounding;\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n    }\n    return round(new Ctor(x), sd, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */ P.toString = P.valueOf = P.val = P.toJSON = P[Symbol.for(\"nodejs.util.inspect.custom\")] = function() {\n    var x = this, e = getBase10Exponent(x), Ctor = x.constructor;\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n};\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n/*\r\n *  add                 P.minus, P.plus\r\n *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n *  exp                 P.exp, P.pow\r\n *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n *                      P.toString, divide, round, toString, exp, ln\r\n *  getLn10             P.log, ln\r\n *  getZeroString       digitsToString, toString\r\n *  ln                  P.log, P.ln, P.pow, exp\r\n *  parseDecimal        Decimal\r\n *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n *                      divide, getLn10, exp, ln\r\n *  subtract            P.minus, P.plus\r\n *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n *  truncate            P.pow\r\n *\r\n *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n */ function add(x, y) {\n    var carry, d, e, i, k, len, xd, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // If either is zero...\n    if (!x.s || !y.s) {\n        // Return x if y is zero.\n        // Return y if y is non-zero.\n        if (!y.s) y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are finite, non-zero numbers with the same sign.\n    k = x.e;\n    e = y.e;\n    xd = xd.slice();\n    i = k - e;\n    // If base 1e7 exponents differ...\n    if (i) {\n        if (i < 0) {\n            d = xd;\n            i = -i;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = k;\n            len = xd.length;\n        }\n        // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n        k = Math.ceil(pr / LOG_BASE);\n        len = k > len ? k + 1 : len + 1;\n        if (i > len) {\n            i = len;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n        d.reverse();\n        for(; i--;)d.push(0);\n        d.reverse();\n    }\n    len = xd.length;\n    i = yd.length;\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n    if (len - i < 0) {\n        i = len;\n        d = yd;\n        yd = xd;\n        xd = d;\n    }\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n    for(carry = 0; i;){\n        carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n        xd[i] %= BASE;\n    }\n    if (carry) {\n        xd.unshift(carry);\n        ++e;\n    }\n    // Remove trailing zeros.\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n    for(len = xd.length; xd[--len] == 0;)xd.pop();\n    y.d = xd;\n    y.e = e;\n    return external ? round(y, pr) : y;\n}\nfunction checkInt32(i, min, max) {\n    if (i !== ~~i || i < min || i > max) {\n        throw Error(invalidArgument + i);\n    }\n}\nfunction digitsToString(d) {\n    var i, k, ws, indexOfLastWord = d.length - 1, str = \"\", w = d[0];\n    if (indexOfLastWord > 0) {\n        str += w;\n        for(i = 1; i < indexOfLastWord; i++){\n            ws = d[i] + \"\";\n            k = LOG_BASE - ws.length;\n            if (k) str += getZeroString(k);\n            str += ws;\n        }\n        w = d[i];\n        ws = w + \"\";\n        k = LOG_BASE - ws.length;\n        if (k) str += getZeroString(k);\n    } else if (w === 0) {\n        return \"0\";\n    }\n    // Remove trailing zeros of last w.\n    for(; w % 10 === 0;)w /= 10;\n    return str + w;\n}\nvar divide = function() {\n    // Assumes non-zero x and k, and hence non-zero result.\n    function multiplyInteger(x, k) {\n        var temp, carry = 0, i = x.length;\n        for(x = x.slice(); i--;){\n            temp = x[i] * k + carry;\n            x[i] = temp % BASE | 0;\n            carry = temp / BASE | 0;\n        }\n        if (carry) x.unshift(carry);\n        return x;\n    }\n    function compare(a, b, aL, bL) {\n        var i, r;\n        if (aL != bL) {\n            r = aL > bL ? 1 : -1;\n        } else {\n            for(i = r = 0; i < aL; i++){\n                if (a[i] != b[i]) {\n                    r = a[i] > b[i] ? 1 : -1;\n                    break;\n                }\n            }\n        }\n        return r;\n    }\n    function subtract(a, b, aL) {\n        var i = 0;\n        // Subtract b from a.\n        for(; aL--;){\n            a[aL] -= i;\n            i = a[aL] < b[aL] ? 1 : 0;\n            a[aL] = i * BASE + a[aL] - b[aL];\n        }\n        // Remove leading zeros.\n        for(; !a[0] && a.length > 1;)a.shift();\n    }\n    return function(x, y, pr, dp) {\n        var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz, Ctor = x.constructor, sign = x.s == y.s ? 1 : -1, xd = x.d, yd = y.d;\n        // Either 0?\n        if (!x.s) return new Ctor(x);\n        if (!y.s) throw Error(decimalError + \"Division by zero\");\n        e = x.e - y.e;\n        yL = yd.length;\n        xL = xd.length;\n        q = new Ctor(sign);\n        qd = q.d = [];\n        // Result exponent may be one less than e.\n        for(i = 0; yd[i] == (xd[i] || 0);)++i;\n        if (yd[i] > (xd[i] || 0)) --e;\n        if (pr == null) {\n            sd = pr = Ctor.precision;\n        } else if (dp) {\n            sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\n        } else {\n            sd = pr;\n        }\n        if (sd < 0) return new Ctor(0);\n        // Convert precision in number of base 10 digits to base 1e7 digits.\n        sd = sd / LOG_BASE + 2 | 0;\n        i = 0;\n        // divisor < 1e7\n        if (yL == 1) {\n            k = 0;\n            yd = yd[0];\n            sd++;\n            // k is the carry.\n            for(; (i < xL || k) && sd--; i++){\n                t = k * BASE + (xd[i] || 0);\n                qd[i] = t / yd | 0;\n                k = t % yd | 0;\n            }\n        // divisor >= 1e7\n        } else {\n            // Normalise xd and yd so highest order digit of yd is >= BASE/2\n            k = BASE / (yd[0] + 1) | 0;\n            if (k > 1) {\n                yd = multiplyInteger(yd, k);\n                xd = multiplyInteger(xd, k);\n                yL = yd.length;\n                xL = xd.length;\n            }\n            xi = yL;\n            rem = xd.slice(0, yL);\n            remL = rem.length;\n            // Add zeros to make remainder as long as divisor.\n            for(; remL < yL;)rem[remL++] = 0;\n            yz = yd.slice();\n            yz.unshift(0);\n            yd0 = yd[0];\n            if (yd[1] >= BASE / 2) ++yd0;\n            do {\n                k = 0;\n                // Compare divisor and remainder.\n                cmp = compare(yd, rem, yL, remL);\n                // If divisor < remainder.\n                if (cmp < 0) {\n                    // Calculate trial digit, k.\n                    rem0 = rem[0];\n                    if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\n                    // k will be how many times the divisor goes into the current remainder.\n                    k = rem0 / yd0 | 0;\n                    //  Algorithm:\n                    //  1. product = divisor * trial digit (k)\n                    //  2. if product > remainder: product -= divisor, k--\n                    //  3. remainder -= product\n                    //  4. if product was < remainder at 2:\n                    //    5. compare new remainder and divisor\n                    //    6. If remainder > divisor: remainder -= divisor, k++\n                    if (k > 1) {\n                        if (k >= BASE) k = BASE - 1;\n                        // product = divisor * trial digit.\n                        prod = multiplyInteger(yd, k);\n                        prodL = prod.length;\n                        remL = rem.length;\n                        // Compare product and remainder.\n                        cmp = compare(prod, rem, prodL, remL);\n                        // product > remainder.\n                        if (cmp == 1) {\n                            k--;\n                            // Subtract divisor from product.\n                            subtract(prod, yL < prodL ? yz : yd, prodL);\n                        }\n                    } else {\n                        // cmp is -1.\n                        // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n                        // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n                        if (k == 0) cmp = k = 1;\n                        prod = yd.slice();\n                    }\n                    prodL = prod.length;\n                    if (prodL < remL) prod.unshift(0);\n                    // Subtract product from remainder.\n                    subtract(rem, prod, remL);\n                    // If product was < previous remainder.\n                    if (cmp == -1) {\n                        remL = rem.length;\n                        // Compare divisor and new remainder.\n                        cmp = compare(yd, rem, yL, remL);\n                        // If divisor < new remainder, subtract divisor from remainder.\n                        if (cmp < 1) {\n                            k++;\n                            // Subtract divisor from remainder.\n                            subtract(rem, yL < remL ? yz : yd, remL);\n                        }\n                    }\n                    remL = rem.length;\n                } else if (cmp === 0) {\n                    k++;\n                    rem = [\n                        0\n                    ];\n                } // if cmp === 1, k will be 0\n                // Add the next digit, k, to the result array.\n                qd[i++] = k;\n                // Update the remainder.\n                if (cmp && rem[0]) {\n                    rem[remL++] = xd[xi] || 0;\n                } else {\n                    rem = [\n                        xd[xi]\n                    ];\n                    remL = 1;\n                }\n            }while ((xi++ < xL || rem[0] !== void 0) && sd--);\n        }\n        // Leading zero?\n        if (!qd[0]) qd.shift();\n        q.e = e;\n        return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\n    };\n}();\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n * significant digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n */ function exp(x, sd) {\n    var denominator, guard, pow, sum, t, wpr, i = 0, k = 0, Ctor = x.constructor, pr = Ctor.precision;\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\n    // exp(0) = 1\n    if (!x.s) return new Ctor(ONE);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    t = new Ctor(0.03125);\n    while(x.abs().gte(0.1)){\n        x = x.times(t); // x = x / 2^5\n        k += 5;\n    }\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n    wpr += guard;\n    denominator = pow = sum = new Ctor(ONE);\n    Ctor.precision = wpr;\n    for(;;){\n        pow = round(pow.times(x), wpr);\n        denominator = denominator.times(++i);\n        t = sum.plus(divide(pow, denominator, wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            while(k--)sum = round(sum.times(sum), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n    }\n}\n// Calculate the base 10 exponent from the base 1e7 exponent.\nfunction getBase10Exponent(x) {\n    var e = x.e * LOG_BASE, w = x.d[0];\n    // Add the number of digits of the first word of the digits array.\n    for(; w >= 10; w /= 10)e++;\n    return e;\n}\nfunction getLn10(Ctor, sd, pr) {\n    if (sd > Ctor.LN10.sd()) {\n        // Reset global state in case the exception is caught.\n        external = true;\n        if (pr) Ctor.precision = pr;\n        throw Error(decimalError + \"LN10 precision limit exceeded\");\n    }\n    return round(new Ctor(Ctor.LN10), sd);\n}\nfunction getZeroString(k) {\n    var zs = \"\";\n    for(; k--;)zs += \"0\";\n    return zs;\n}\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n * digits.\r\n *\r\n *  ln(n) is non-terminating (n != 1)\r\n *\r\n */ function ln(y, sd) {\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2, n = 1, guard = 10, x = y, xd = x.d, Ctor = x.constructor, pr = Ctor.precision;\n    // ln(-x) = NaN\n    // ln(0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // ln(1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    if (x.eq(10)) {\n        if (sd == null) external = true;\n        return getLn10(Ctor, wpr);\n    }\n    wpr += guard;\n    Ctor.precision = wpr;\n    c = digitsToString(xd);\n    c0 = c.charAt(0);\n    e = getBase10Exponent(x);\n    if (Math.abs(e) < 1.5e15) {\n        // Argument reduction.\n        // The series converges faster the closer the argument is to 1, so using\n        // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n        // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n        // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n        // later be divided by this number, then separate out the power of 10 using\n        // ln(a*10^b) = ln(a) + b*ln(10).\n        // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n        //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n        // max n is 6 (gives 0.7 - 1.3)\n        while(c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3){\n            x = x.times(y);\n            c = digitsToString(x.d);\n            c0 = c.charAt(0);\n            n++;\n        }\n        e = getBase10Exponent(x);\n        if (c0 > 1) {\n            x = new Ctor(\"0.\" + c);\n            e++;\n        } else {\n            x = new Ctor(c0 + \".\" + c.slice(1));\n        }\n    } else {\n        // The argument reduction method above may result in overflow if the argument y is a massive\n        // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n        // function using ln(x*10^e) = ln(x) + e*ln(10).\n        t = getLn10(Ctor, wpr + 2, pr).times(e + \"\");\n        x = ln(new Ctor(c0 + \".\" + c.slice(1)), wpr - guard).plus(t);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(x, pr)) : x;\n    }\n    // x is reduced to a value near 1.\n    // Taylor series.\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\n    x2 = round(x.times(x), wpr);\n    denominator = 3;\n    for(;;){\n        numerator = round(numerator.times(x2), wpr);\n        t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            sum = sum.times(2);\n            // Reverse the argument reduction.\n            if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + \"\"));\n            sum = divide(sum, new Ctor(n), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n        denominator += 2;\n    }\n}\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */ function parseDecimal(x, str) {\n    var e, i, len;\n    // Decimal point?\n    if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n    // Exponential form?\n    if ((i = str.search(/e/i)) > 0) {\n        // Determine exponent.\n        if (e < 0) e = i;\n        e += +str.slice(i + 1);\n        str = str.substring(0, i);\n    } else if (e < 0) {\n        // Integer.\n        e = str.length;\n    }\n    // Determine leading zeros.\n    for(i = 0; str.charCodeAt(i) === 48;)++i;\n    // Determine trailing zeros.\n    for(len = str.length; str.charCodeAt(len - 1) === 48;)--len;\n    str = str.slice(i, len);\n    if (str) {\n        len -= i;\n        e = e - i - 1;\n        x.e = mathfloor(e / LOG_BASE);\n        x.d = [];\n        // Transform base\n        // e is the base 10 exponent.\n        // i is where to slice str to get the first word of the digits array.\n        i = (e + 1) % LOG_BASE;\n        if (e < 0) i += LOG_BASE;\n        if (i < len) {\n            if (i) x.d.push(+str.slice(0, i));\n            for(len -= LOG_BASE; i < len;)x.d.push(+str.slice(i, i += LOG_BASE));\n            str = str.slice(i);\n            i = LOG_BASE - str.length;\n        } else {\n            i -= len;\n        }\n        for(; i--;)str += \"0\";\n        x.d.push(+str);\n        if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\n    } else {\n        // Zero.\n        x.s = 0;\n        x.e = 0;\n        x.d = [\n            0\n        ];\n    }\n    return x;\n}\n/*\r\n * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n */ function round(x, sd, rm) {\n    var i, j, k, n, rd, doRound, w, xdi, xd = x.d;\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // n: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n    // Get the length of the first word of the digits array xd.\n    for(n = 1, k = xd[0]; k >= 10; k /= 10)n++;\n    i = sd - n;\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n        i += LOG_BASE;\n        j = sd;\n        w = xd[xdi = 0];\n    } else {\n        xdi = Math.ceil((i + 1) / LOG_BASE);\n        k = xd.length;\n        if (xdi >= k) return x;\n        w = k = xd[xdi];\n        // Get the number of digits of w.\n        for(n = 1; k >= 10; k /= 10)n++;\n        // Get the index of rd within w.\n        i %= LOG_BASE;\n        // Get the index of rd within w, adjusted for leading zeros.\n        // The number of leading zeros of w is given by LOG_BASE - n.\n        j = i - LOG_BASE + n;\n    }\n    if (rm !== void 0) {\n        k = mathpow(10, n - j - 1);\n        // Get the rounding digit at index j of w.\n        rd = w / k % 10 | 0;\n        // Are there any non-zero digits after the rounding digit?\n        doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\n        // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\n        // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\n        // 714.\n        doRound = rm < 4 ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 && (i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    }\n    if (sd < 1 || !xd[0]) {\n        if (doRound) {\n            k = getBase10Exponent(x);\n            xd.length = 1;\n            // Convert sd to decimal places.\n            sd = sd - k - 1;\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n            xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n            x.e = mathfloor(-sd / LOG_BASE) || 0;\n        } else {\n            xd.length = 1;\n            // Zero.\n            xd[0] = x.e = x.s = 0;\n        }\n        return x;\n    }\n    // Remove excess digits.\n    if (i == 0) {\n        xd.length = xdi;\n        k = 1;\n        xdi--;\n    } else {\n        xd.length = xdi + 1;\n        k = mathpow(10, LOG_BASE - i);\n        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n        // j > 0 means i > number of leading zeros of w.\n        xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (doRound) {\n        for(;;){\n            // Is the digit to be rounded up in the first word of xd?\n            if (xdi == 0) {\n                if ((xd[0] += k) == BASE) {\n                    xd[0] = 1;\n                    ++x.e;\n                }\n                break;\n            } else {\n                xd[xdi] += k;\n                if (xd[xdi] != BASE) break;\n                xd[xdi--] = 0;\n                k = 1;\n            }\n        }\n    }\n    // Remove trailing zeros.\n    for(i = xd.length; xd[--i] === 0;)xd.pop();\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\n        throw Error(exponentOutOfRange + getBase10Exponent(x));\n    }\n    return x;\n}\nfunction subtract(x, y) {\n    var d, e, i, j, k, len, xd, xe, xLTy, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // Return y negated if x is zero.\n    // Return x if y is zero and x is non-zero.\n    if (!x.s || !y.s) {\n        if (y.s) y.s = -y.s;\n        else y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are non-zero numbers with the same sign.\n    e = y.e;\n    xe = x.e;\n    xd = xd.slice();\n    k = xe - e;\n    // If exponents differ...\n    if (k) {\n        xLTy = k < 0;\n        if (xLTy) {\n            d = xd;\n            k = -k;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = xe;\n            len = xd.length;\n        }\n        // Numbers with massively different exponents would result in a very high number of zeros\n        // needing to be prepended, but this can be avoided while still ensuring correct rounding by\n        // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n        i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n        if (k > i) {\n            k = i;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents.\n        d.reverse();\n        for(i = k; i--;)d.push(0);\n        d.reverse();\n    // Base 1e7 exponents equal.\n    } else {\n        // Check digits to determine which is the bigger number.\n        i = xd.length;\n        len = yd.length;\n        xLTy = i < len;\n        if (xLTy) len = i;\n        for(i = 0; i < len; i++){\n            if (xd[i] != yd[i]) {\n                xLTy = xd[i] < yd[i];\n                break;\n            }\n        }\n        k = 0;\n    }\n    if (xLTy) {\n        d = xd;\n        xd = yd;\n        yd = d;\n        y.s = -y.s;\n    }\n    len = xd.length;\n    // Append zeros to xd if shorter.\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\n    for(i = yd.length - len; i > 0; --i)xd[len++] = 0;\n    // Subtract yd from xd.\n    for(i = yd.length; i > k;){\n        if (xd[--i] < yd[i]) {\n            for(j = i; j && xd[--j] === 0;)xd[j] = BASE - 1;\n            --xd[j];\n            xd[i] += BASE;\n        }\n        xd[i] -= yd[i];\n    }\n    // Remove trailing zeros.\n    for(; xd[--len] === 0;)xd.pop();\n    // Remove leading zeros and adjust exponent accordingly.\n    for(; xd[0] === 0; xd.shift())--e;\n    // Zero?\n    if (!xd[0]) return new Ctor(0);\n    y.d = xd;\n    y.e = e;\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\n    return external ? round(y, pr) : y;\n}\nfunction toString(x, isExp, sd) {\n    var k, e = getBase10Exponent(x), str = digitsToString(x.d), len = str.length;\n    if (isExp) {\n        if (sd && (k = sd - len) > 0) {\n            str = str.charAt(0) + \".\" + str.slice(1) + getZeroString(k);\n        } else if (len > 1) {\n            str = str.charAt(0) + \".\" + str.slice(1);\n        }\n        str = str + (e < 0 ? \"e\" : \"e+\") + e;\n    } else if (e < 0) {\n        str = \"0.\" + getZeroString(-e - 1) + str;\n        if (sd && (k = sd - len) > 0) str += getZeroString(k);\n    } else if (e >= len) {\n        str += getZeroString(e + 1 - len);\n        if (sd && (k = sd - e - 1) > 0) str = str + \".\" + getZeroString(k);\n    } else {\n        if ((k = e + 1) < len) str = str.slice(0, k) + \".\" + str.slice(k);\n        if (sd && (k = sd - len) > 0) {\n            if (e + 1 === len) str += \".\";\n            str += getZeroString(k);\n        }\n    }\n    return x.s < 0 ? \"-\" + str : str;\n}\n// Does not strip trailing zeros.\nfunction truncate(arr, len) {\n    if (arr.length > len) {\n        arr.length = len;\n        return true;\n    }\n}\n// Decimal methods\n/*\r\n *  clone\r\n *  config/set\r\n */ /*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */ function clone(obj) {\n    var i, p, ps;\n    /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * value {number|string|Decimal} A numeric value.\r\n   *\r\n   */ function Decimal(value) {\n        var x = this;\n        // Decimal called without new.\n        if (!(x instanceof Decimal)) return new Decimal(value);\n        // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n        // which points to Object.\n        x.constructor = Decimal;\n        // Duplicate.\n        if (value instanceof Decimal) {\n            x.s = value.s;\n            x.e = value.e;\n            x.d = (value = value.d) ? value.slice() : value;\n            return;\n        }\n        if (typeof value === \"number\") {\n            // Reject Infinity/NaN.\n            if (value * 0 !== 0) {\n                throw Error(invalidArgument + value);\n            }\n            if (value > 0) {\n                x.s = 1;\n            } else if (value < 0) {\n                value = -value;\n                x.s = -1;\n            } else {\n                x.s = 0;\n                x.e = 0;\n                x.d = [\n                    0\n                ];\n                return;\n            }\n            // Fast path for small integers.\n            if (value === ~~value && value < 1e7) {\n                x.e = 0;\n                x.d = [\n                    value\n                ];\n                return;\n            }\n            return parseDecimal(x, value.toString());\n        } else if (typeof value !== \"string\") {\n            throw Error(invalidArgument + value);\n        }\n        // Minus sign?\n        if (value.charCodeAt(0) === 45) {\n            value = value.slice(1);\n            x.s = -1;\n        } else {\n            x.s = 1;\n        }\n        if (isDecimal.test(value)) parseDecimal(x, value);\n        else throw Error(invalidArgument + value);\n    }\n    Decimal.prototype = P;\n    Decimal.ROUND_UP = 0;\n    Decimal.ROUND_DOWN = 1;\n    Decimal.ROUND_CEIL = 2;\n    Decimal.ROUND_FLOOR = 3;\n    Decimal.ROUND_HALF_UP = 4;\n    Decimal.ROUND_HALF_DOWN = 5;\n    Decimal.ROUND_HALF_EVEN = 6;\n    Decimal.ROUND_HALF_CEIL = 7;\n    Decimal.ROUND_HALF_FLOOR = 8;\n    Decimal.clone = clone;\n    Decimal.config = Decimal.set = config;\n    if (obj === void 0) obj = {};\n    if (obj) {\n        ps = [\n            \"precision\",\n            \"rounding\",\n            \"toExpNeg\",\n            \"toExpPos\",\n            \"LN10\"\n        ];\n        for(i = 0; i < ps.length;)if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n    Decimal.config(obj);\n    return Decimal;\n}\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */ function config(obj) {\n    if (!obj || typeof obj !== \"object\") {\n        throw Error(decimalError + \"Object expected\");\n    }\n    var i, p, v, ps = [\n        \"precision\",\n        1,\n        MAX_DIGITS,\n        \"rounding\",\n        0,\n        8,\n        \"toExpNeg\",\n        -1 / 0,\n        0,\n        \"toExpPos\",\n        0,\n        1 / 0\n    ];\n    for(i = 0; i < ps.length; i += 3){\n        if ((v = obj[p = ps[i]]) !== void 0) {\n            if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\n            else throw Error(invalidArgument + p + \": \" + v);\n        }\n    }\n    if ((v = obj[p = \"LN10\"]) !== void 0) {\n        if (v == Math.LN10) this[p] = new this(v);\n        else throw Error(invalidArgument + p + \": \" + v);\n    }\n    return this;\n}\n// Create and configure initial Decimal constructor.\nvar Decimal = clone(defaults);\n// Internal constant.\nONE = new Decimal(1);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Decimal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/decimal.js-light/decimal.mjs\n");

/***/ })

};
;