package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.notification.NotificationDto;
import com.grocease.entity.User;
import com.grocease.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
@Slf4j
public class NotificationController {

    private final NotificationService notificationService;

    @PostMapping("/register-token")
    public ResponseEntity<ApiResponse<String>> registerDeviceToken(
            @AuthenticationPrincipal User user,
            @RequestParam String deviceToken,
            @RequestParam(required = false) String deviceType,
            @RequestParam(required = false) String deviceId) {
        log.info("Registering device token for user: {}", user.getId());
        
        notificationService.registerDeviceToken(user.getId(), deviceToken, deviceType, deviceId);
        return ResponseEntity.ok(ApiResponse.success("Device token registered successfully", 
                "You will now receive push notifications"));
    }

    @PostMapping("/unregister-token")
    public ResponseEntity<ApiResponse<String>> unregisterDeviceToken(@RequestParam String deviceToken) {
        log.info("Unregistering device token");
        
        notificationService.unregisterDeviceToken(deviceToken);
        return ResponseEntity.ok(ApiResponse.success("Device token unregistered successfully", 
                "You will no longer receive push notifications on this device"));
    }

    @GetMapping("/history")
    public ResponseEntity<Page<NotificationDto>> getUserNotificationHistory(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        log.info("Getting notification history for user: {}", user.getId());
        
        Pageable pageable = PageRequest.of(page, size);
        Page<NotificationDto> notifications = notificationService.getNotificationHistory(user.getId(), pageable);
        return ResponseEntity.ok(notifications);
    }
}
