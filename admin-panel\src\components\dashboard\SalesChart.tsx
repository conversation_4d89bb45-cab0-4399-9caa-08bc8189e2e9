'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts'
import { SalesData } from '@/types'
import { formatCurrency } from '@/lib/utils'

interface SalesChartProps {
  data: SalesData[]
  title: string
  description?: string
  type?: 'line' | 'bar'
}

export default function SalesChart({ 
  data, 
  title, 
  description, 
  type = 'line' 
}: SalesChartProps) {
  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'totalRevenue') {
      return [formatCurrency(value), 'Revenue']
    }
    if (name === 'orderCount') {
      return [value, 'Orders']
    }
    if (name === 'averageOrderValue') {
      return [formatCurrency(value), 'Avg Order Value']
    }
    return [value, name]
  }

  const formatXAxisLabel = (value: string) => {
    // Format period labels (e.g., "2024-01" -> "Jan 2024")
    if (value.includes('-W')) {
      return `Week ${value.split('-W')[1]}`
    }
    if (value.match(/^\d{4}-\d{2}$/)) {
      const [year, month] = value.split('-')
      const date = new Date(parseInt(year), parseInt(month) - 1)
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    }
    return value
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          {type === 'line' ? (
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="period" 
                tickFormatter={formatXAxisLabel}
                fontSize={12}
              />
              <YAxis fontSize={12} />
              <Tooltip 
                formatter={formatTooltipValue}
                labelFormatter={(label) => `Period: ${formatXAxisLabel(label)}`}
              />
              <Line 
                type="monotone" 
                dataKey="totalRevenue" 
                stroke="hsl(var(--primary))" 
                strokeWidth={2}
                dot={{ fill: 'hsl(var(--primary))' }}
              />
              <Line 
                type="monotone" 
                dataKey="orderCount" 
                stroke="hsl(var(--destructive))" 
                strokeWidth={2}
                dot={{ fill: 'hsl(var(--destructive))' }}
              />
            </LineChart>
          ) : (
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="period" 
                tickFormatter={formatXAxisLabel}
                fontSize={12}
              />
              <YAxis fontSize={12} />
              <Tooltip 
                formatter={formatTooltipValue}
                labelFormatter={(label) => `Period: ${formatXAxisLabel(label)}`}
              />
              <Bar 
                dataKey="totalRevenue" 
                fill="hsl(var(--primary))" 
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          )}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
