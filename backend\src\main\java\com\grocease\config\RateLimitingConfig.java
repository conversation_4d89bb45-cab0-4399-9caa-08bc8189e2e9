package com.grocease.config;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
public class RateLimitingConfig {

    private final ConcurrentHashMap<String, Bucket> buckets = new ConcurrentHashMap<>();

    @Bean
    public ConcurrentHashMap<String, Bucket> rateLimitBuckets() {
        return buckets;
    }

    public Bucket createNewBucket(String key, long capacity, Duration refillPeriod, long refillTokens) {
        Bandwidth limit = Bandwidth.classic(capacity, Refill.intervally(refillTokens, refillPeriod));
        return Bucket.builder()
                .addLimit(limit)
                .build();
    }

    public Bucket getOrCreateBucket(String key, long capacity, Duration refillPeriod, long refillTokens) {
        return buckets.computeIfAbsent(key, k -> createNewBucket(k, capacity, refillPeriod, refillTokens));
    }

    // Notification rate limits
    public Bucket getNotificationBucket(Long userId) {
        String key = "notification_user_" + userId;
        return getOrCreateBucket(key, 10, Duration.ofHours(1), 10); // 10 notifications per hour
    }

    public Bucket getBroadcastBucket() {
        String key = "notification_broadcast";
        return getOrCreateBucket(key, 5, Duration.ofHours(1), 5); // 5 broadcasts per hour
    }

    // API rate limits
    public Bucket getApiRateLimitBucket(String clientId) {
        String key = "api_" + clientId;
        return getOrCreateBucket(key, 100, Duration.ofMinutes(1), 100); // 100 requests per minute
    }
}
