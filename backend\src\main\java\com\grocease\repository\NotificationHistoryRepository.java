package com.grocease.repository;

import com.grocease.entity.NotificationHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface NotificationHistoryRepository extends JpaRepository<NotificationHistory, Long> {
    
    Page<NotificationHistory> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    Page<NotificationHistory> findByOrderByCreatedAtDesc(Pageable pageable);
    
    @Query("SELECT COUNT(n) FROM NotificationHistory n WHERE n.user.id = :userId AND n.createdAt >= :since")
    Long countNotificationsSentToUserSince(@Param("userId") Long userId, @Param("since") LocalDateTime since);
    
    @Query("SELECT COUNT(n) FROM NotificationHistory n WHERE n.createdAt >= :since")
    Long countNotificationsSentSince(@Param("since") LocalDateTime since);
}
