package com.grocease.service;

import com.google.firebase.messaging.*;
import com.grocease.dto.notification.NotificationDto;
import com.grocease.dto.notification.SendNotificationRequest;
import com.grocease.entity.NotificationHistory;
import com.grocease.entity.User;
import com.grocease.entity.UserDeviceToken;
import com.grocease.repository.NotificationHistoryRepository;
import com.grocease.repository.UserDeviceTokenRepository;
import com.grocease.repository.UserRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final FirebaseMessaging firebaseMessaging;
    private final UserDeviceTokenRepository deviceTokenRepository;
    private final NotificationHistoryRepository notificationHistoryRepository;
    private final UserRepository userRepository;
    private final DtoMapper dtoMapper;

    @Transactional
    public void registerDeviceToken(Long userId, String deviceToken, String deviceType, String deviceId) {
        log.info("Registering device token for user: {}", userId);
        
        // Check if token already exists
        deviceTokenRepository.findByDeviceToken(deviceToken)
                .ifPresentOrElse(
                    existingToken -> {
                        existingToken.setIsActive(true);
                        existingToken.setDeviceType(deviceType);
                        existingToken.setDeviceId(deviceId);
                        deviceTokenRepository.save(existingToken);
                    },
                    () -> {
                        User user = userRepository.findById(userId)
                                .orElseThrow(() -> new RuntimeException("User not found"));
                        
                        UserDeviceToken newToken = UserDeviceToken.builder()
                                .user(user)
                                .deviceToken(deviceToken)
                                .deviceType(deviceType)
                                .deviceId(deviceId)
                                .isActive(true)
                                .build();
                        
                        deviceTokenRepository.save(newToken);
                        
                        // Deactivate other tokens for this user
                        deviceTokenRepository.deactivateOtherTokensForUser(userId, deviceToken);
                    }
                );
    }

    @Transactional
    public void unregisterDeviceToken(String deviceToken) {
        log.info("Unregistering device token: {}", deviceToken);
        deviceTokenRepository.deactivateToken(deviceToken);
    }

    @Transactional
    public void sendNotification(SendNotificationRequest request) {
        log.info("Sending notification: {} to {} users", request.getTitle(), 
                request.getUserIds() != null ? request.getUserIds().size() : "all");

        List<UserDeviceToken> targetTokens = getTargetDeviceTokens(request.getUserIds());
        
        if (targetTokens.isEmpty()) {
            log.warn("No active device tokens found for notification");
            return;
        }

        // Create notification message
        Notification notification = Notification.builder()
                .setTitle(request.getTitle())
                .setBody(request.getMessage())
                .build();

        // Send to each device token
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (UserDeviceToken deviceToken : targetTokens) {
            CompletableFuture<Void> future = sendToDevice(
                    deviceToken, notification, request.getTitle(), request.getMessage(),
                    request.getData(), request.getType(), request.getIsBroadcast());
            futures.add(future);
        }

        // Wait for all notifications to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> log.info("All notifications sent successfully"))
                .exceptionally(throwable -> {
                    log.error("Error sending some notifications", throwable);
                    return null;
                });
    }

    private CompletableFuture<Void> sendToDevice(UserDeviceToken deviceToken, Notification notification,
                                               String title, String messageText, Map<String, String> data,
                                               NotificationHistory.NotificationType type, Boolean isBroadcast) {
        return CompletableFuture.runAsync(() -> {
            try {
                Message.Builder messageBuilder = Message.builder()
                        .setToken(deviceToken.getDeviceToken())
                        .setNotification(notification);

                if (data != null && !data.isEmpty()) {
                    messageBuilder.putAllData(data);
                }

                Message message = messageBuilder.build();
                
                if (firebaseMessaging != null) {
                    String response = firebaseMessaging.send(message);
                    log.info("Successfully sent message to {}: {}", deviceToken.getDeviceToken(), response);

                    // Save notification history
                    saveNotificationHistory(deviceToken.getUser(), title,
                            messageText, type, deviceToken.getDeviceToken(),
                            NotificationHistory.NotificationStatus.SENT, response, null, isBroadcast);
                } else {
                    log.warn("Firebase messaging not available, notification not sent");
                    saveNotificationHistory(deviceToken.getUser(), title,
                            messageText, type, deviceToken.getDeviceToken(),
                            NotificationHistory.NotificationStatus.FAILED, null,
                            "Firebase messaging not configured", isBroadcast);
                }
                
            } catch (Exception e) {
                log.error("Error sending notification to device: {}", deviceToken.getDeviceToken(), e);
                saveNotificationHistory(deviceToken.getUser(), title,
                        messageText, type, deviceToken.getDeviceToken(),
                        NotificationHistory.NotificationStatus.FAILED, null, e.getMessage(), isBroadcast);
            }
        });
    }

    private List<UserDeviceToken> getTargetDeviceTokens(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return deviceTokenRepository.findByIsActiveTrue();
        } else {
            List<UserDeviceToken> tokens = new ArrayList<>();
            for (Long userId : userIds) {
                tokens.addAll(deviceTokenRepository.findByUserIdAndIsActiveTrue(userId));
            }
            return tokens;
        }
    }

    @Transactional
    protected void saveNotificationHistory(User user, String title, String message, 
                                         NotificationHistory.NotificationType type, String deviceToken,
                                         NotificationHistory.NotificationStatus status, String firebaseMessageId,
                                         String errorMessage, Boolean isBroadcast) {
        NotificationHistory history = NotificationHistory.builder()
                .user(user)
                .title(title)
                .message(message)
                .type(type)
                .deviceToken(deviceToken)
                .status(status)
                .firebaseMessageId(firebaseMessageId)
                .errorMessage(errorMessage)
                .isBroadcast(isBroadcast != null ? isBroadcast : false)
                .build();

        notificationHistoryRepository.save(history);
    }

    public Page<NotificationDto> getNotificationHistory(Long userId, Pageable pageable) {
        Page<NotificationHistory> historyPage;
        
        if (userId != null) {
            historyPage = notificationHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        } else {
            historyPage = notificationHistoryRepository.findByOrderByCreatedAtDesc(pageable);
        }

        return historyPage.map(this::toNotificationDto);
    }

    private NotificationDto toNotificationDto(NotificationHistory history) {
        return NotificationDto.builder()
                .id(history.getId())
                .title(history.getTitle())
                .message(history.getMessage())
                .type(history.getType())
                .status(history.getStatus())
                .isBroadcast(history.getIsBroadcast())
                .sentAt(history.getCreatedAt())
                .errorMessage(history.getErrorMessage())
                .build();
    }

    // Rate limiting check
    public boolean canSendNotification(Long userId) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Long notificationCount = notificationHistoryRepository.countNotificationsSentToUserSince(userId, oneHourAgo);
        return notificationCount < 10; // Max 10 notifications per hour per user
    }

    public boolean canSendBroadcast() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Long broadcastCount = notificationHistoryRepository.countNotificationsSentSince(oneHourAgo);
        return broadcastCount < 5; // Max 5 broadcasts per hour
    }
}
