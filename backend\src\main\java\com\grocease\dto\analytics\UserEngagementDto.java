package com.grocease.dto.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserEngagementDto {
    private LocalDate date;
    private String period;
    private Long newUsers;
    private Long activeUsers;
    private Long totalUsers;
    private Double retentionRate;
    private Long returningUsers;
}
