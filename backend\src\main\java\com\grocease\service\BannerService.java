package com.grocease.service;

import com.grocease.dto.banner.BannerDto;
import com.grocease.entity.Banner;
import com.grocease.repository.BannerRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class BannerService {

    private final BannerRepository bannerRepository;
    private final DtoMapper dtoMapper;

    public List<BannerDto> getAllActiveBanners() {
        List<Banner> banners = bannerRepository.findByIsActiveTrueOrderBySortOrderAsc();
        return dtoMapper.toBannerDtoList(banners);
    }
}
