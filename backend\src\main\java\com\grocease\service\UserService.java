package com.grocease.service;

import com.grocease.dto.user.AddressDto;
import com.grocease.dto.user.CreateAddressRequest;
import com.grocease.dto.user.UserDto;
import com.grocease.entity.Address;
import com.grocease.entity.User;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.AddressRepository;
import com.grocease.repository.UserRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService implements UserDetailsService {

    private final UserRepository userRepository;
    private final AddressRepository addressRepository;
    private final DtoMapper dtoMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return userRepository.findByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + username));
    }

    public UserDto getUserById(Long userId) {
        User user = userRepository.findByIdWithAddresses(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        return dtoMapper.toUserDto(user);
    }

    public UserDto getUserByEmail(String email) {
        User user = userRepository.findByEmailWithAddresses(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));
        return dtoMapper.toUserDto(user);
    }

    @Transactional
    public UserDto updateUser(Long userId, UserDto userDto) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        user.setName(userDto.getName());
        user.setPhone(userDto.getPhone());
        user.setAvatar(userDto.getAvatar());

        User savedUser = userRepository.save(user);
        return dtoMapper.toUserDto(savedUser);
    }

    public List<AddressDto> getUserAddresses(Long userId) {
        List<Address> addresses = addressRepository.findByUserIdOrderByIsDefaultDescCreatedAtDesc(userId);
        return addresses.stream()
                .map(dtoMapper::toAddressDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public AddressDto createAddress(Long userId, CreateAddressRequest request) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // If this is set as default, clear other default addresses
        if (request.getIsDefault()) {
            addressRepository.clearDefaultAddressForUser(userId);
        }

        Address address = Address.builder()
                .type(request.getType())
                .street(request.getStreet())
                .city(request.getCity())
                .state(request.getState())
                .zipCode(request.getZipCode())
                .isDefault(request.getIsDefault())
                .user(user)
                .build();

        Address savedAddress = addressRepository.save(address);
        return dtoMapper.toAddressDto(savedAddress);
    }

    @Transactional
    public AddressDto updateAddress(Long userId, Long addressId, CreateAddressRequest request) {
        Address address = addressRepository.findById(addressId)
                .orElseThrow(() -> new ResourceNotFoundException("Address not found with id: " + addressId));

        if (!address.getUser().getId().equals(userId)) {
            throw new IllegalArgumentException("Address does not belong to user");
        }

        // If this is set as default, clear other default addresses
        if (request.getIsDefault()) {
            addressRepository.clearDefaultAddressForUser(userId);
        }

        address.setType(request.getType());
        address.setStreet(request.getStreet());
        address.setCity(request.getCity());
        address.setState(request.getState());
        address.setZipCode(request.getZipCode());
        address.setIsDefault(request.getIsDefault());

        Address savedAddress = addressRepository.save(address);
        return dtoMapper.toAddressDto(savedAddress);
    }

    @Transactional
    public void deleteAddress(Long userId, Long addressId) {
        Address address = addressRepository.findById(addressId)
                .orElseThrow(() -> new ResourceNotFoundException("Address not found with id: " + addressId));

        if (!address.getUser().getId().equals(userId)) {
            throw new IllegalArgumentException("Address does not belong to user");
        }

        addressRepository.delete(address);
    }

    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }
}
