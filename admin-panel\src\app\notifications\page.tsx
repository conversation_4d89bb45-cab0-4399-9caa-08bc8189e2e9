'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { 
  Send, 
  Bell, 
  Users, 
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react'
import apiClient from '@/lib/api'
import { NotificationType, SendNotificationRequest } from '@/types'
import { formatDateTime } from '@/lib/utils'

const NOTIFICATION_TYPE_LABELS = {
  ORDER_CONFIRMATION: 'Order Confirmation',
  ORDER_PREPARING: 'Order Preparing',
  ORDER_OUT_FOR_DELIVERY: 'Out for Delivery',
  ORDER_DELIVERED: 'Order Delivered',
  ORDER_CANCELLED: 'Order Cancelled',
  BIRTHDAY_WISH: 'Birthday Wish',
  PROMOTIONAL_OFFER: 'Promotional Offer',
  NEW_PRODUCT: 'New Product',
  LOW_STOCK_ALERT: 'Low Stock Alert',
  CART_REMINDER: 'Cart Reminder',
  GENERAL: 'General',
}

const STATUS_COLORS = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  SENT: 'bg-blue-100 text-blue-800',
  DELIVERED: 'bg-green-100 text-green-800',
  FAILED: 'bg-red-100 text-red-800',
}

const STATUS_ICONS = {
  PENDING: Clock,
  SENT: CheckCircle,
  DELIVERED: CheckCircle,
  FAILED: XCircle,
}

export default function NotificationsPage() {
  const [page, setPage] = useState(0)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [formData, setFormData] = useState<SendNotificationRequest>({
    title: '',
    message: '',
    type: 'GENERAL',
    isBroadcast: true,
  })

  const queryClient = useQueryClient()

  const { data: notificationsData, isLoading } = useQuery({
    queryKey: ['notifications', page],
    queryFn: () => apiClient.getNotificationHistory(page, 20),
  })

  const sendNotificationMutation = useMutation({
    mutationFn: (data: SendNotificationRequest) => apiClient.sendNotification(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
      setIsDialogOpen(false)
      setFormData({
        title: '',
        message: '',
        type: 'GENERAL',
        isBroadcast: true,
      })
    },
  })

  const handleSendNotification = () => {
    if (!formData.title || !formData.message) return
    sendNotificationMutation.mutate(formData)
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
            <p className="text-muted-foreground">
              Send and manage push notifications
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Send className="h-4 w-4 mr-2" />
                Send Notification
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Send Notification</DialogTitle>
                <DialogDescription>
                  Send a push notification to users
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    placeholder="Notification title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="Notification message"
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => setFormData({ ...formData, type: value as NotificationType })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(NOTIFICATION_TYPE_LABELS).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="broadcast"
                    checked={formData.isBroadcast}
                    onChange={(e) => setFormData({ ...formData, isBroadcast: e.target.checked })}
                  />
                  <Label htmlFor="broadcast">Send to all users</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSendNotification}
                  disabled={!formData.title || !formData.message || sendNotificationMutation.isPending}
                >
                  {sendNotificationMutation.isPending ? 'Sending...' : 'Send'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Tabs defaultValue="history" className="space-y-6">
          <TabsList>
            <TabsTrigger value="history">Notification History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-6">
            {/* Notification History */}
            <Card>
              <CardHeader>
                <CardTitle>Notification History</CardTitle>
                <CardDescription>
                  {notificationsData?.pagination.total || 0} total notifications sent
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Sent At</TableHead>
                          <TableHead>Broadcast</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {notificationsData?.data.map((notification) => {
                          const StatusIcon = STATUS_ICONS[notification.status]
                          return (
                            <TableRow key={notification.id}>
                              <TableCell>
                                <div>
                                  <p className="font-medium">{notification.title}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {notification.message}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {NOTIFICATION_TYPE_LABELS[notification.type]}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <StatusIcon className="h-4 w-4" />
                                  <Badge 
                                    className={STATUS_COLORS[notification.status]}
                                    variant="secondary"
                                  >
                                    {notification.status}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>
                                {formatDateTime(notification.sentAt)}
                              </TableCell>
                              <TableCell>
                                {notification.isBroadcast ? (
                                  <Badge variant="default">
                                    <Users className="h-3 w-3 mr-1" />
                                    Broadcast
                                  </Badge>
                                ) : (
                                  <Badge variant="secondary">
                                    <MessageSquare className="h-3 w-3 mr-1" />
                                    Targeted
                                  </Badge>
                                )}
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>

                    {/* Pagination */}
                    {notificationsData?.pagination && (
                      <div className="flex items-center justify-between mt-4">
                        <p className="text-sm text-muted-foreground">
                          Showing {page * 20 + 1} to {Math.min((page + 1) * 20, notificationsData.pagination.total)} of{' '}
                          {notificationsData.pagination.total} notifications
                        </p>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage(page - 1)}
                            disabled={page === 0}
                          >
                            <ChevronLeft className="h-4 w-4" />
                            Previous
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPage(page + 1)}
                            disabled={page >= notificationsData.pagination.totalPages - 1}
                          >
                            Next
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Notification Analytics */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Sent</CardTitle>
                  <Send className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {notificationsData?.pagination.total || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    All time notifications
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Delivered</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {notificationsData?.data.filter(n => n.status === 'DELIVERED').length || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Successfully delivered
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Failed</CardTitle>
                  <XCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {notificationsData?.data.filter(n => n.status === 'FAILED').length || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Delivery failed
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Broadcasts</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {notificationsData?.data.filter(n => n.isBroadcast).length || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Broadcast messages
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
