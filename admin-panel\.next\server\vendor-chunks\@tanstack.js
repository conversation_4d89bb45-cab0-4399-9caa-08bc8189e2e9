"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #focused;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#setup = (onFocus)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const listener = ()=>onFocus();\n                window.addEventListener(\"visibilitychange\", listener, false);\n                return ()=>{\n                    window.removeEventListener(\"visibilitychange\", listener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup((focused)=>{\n            if (typeof focused === \"boolean\") {\n                this.setFocused(focused);\n            } else {\n                this.onFocus();\n            }\n        });\n    }\n    setFocused(focused) {\n        const changed = this.#focused !== focused;\n        if (changed) {\n            this.#focused = focused;\n            this.onFocus();\n        }\n    }\n    onFocus() {\n        const isFocused = this.isFocused();\n        this.listeners.forEach((listener)=>{\n            listener(isFocused);\n        });\n    }\n    isFocused() {\n        if (typeof this.#focused === \"boolean\") {\n            return this.#focused;\n        }\n        return globalThis.document?.visibilityState !== \"hidden\";\n    }\n};\nvar focusManager = new FocusManager();\n //# sourceMappingURL=focusManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n    return {\n        onFetch: (context, query)=>{\n            const options = context.options;\n            const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n            const oldPages = context.state.data?.pages || [];\n            const oldPageParams = context.state.data?.pageParams || [];\n            let result = {\n                pages: [],\n                pageParams: []\n            };\n            let currentPage = 0;\n            const fetchFn = async ()=>{\n                let cancelled = false;\n                const addSignalProperty = (object)=>{\n                    Object.defineProperty(object, \"signal\", {\n                        enumerable: true,\n                        get: ()=>{\n                            if (context.signal.aborted) {\n                                cancelled = true;\n                            } else {\n                                context.signal.addEventListener(\"abort\", ()=>{\n                                    cancelled = true;\n                                });\n                            }\n                            return context.signal;\n                        }\n                    });\n                };\n                const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n                const fetchPage = async (data, param, previous)=>{\n                    if (cancelled) {\n                        return Promise.reject();\n                    }\n                    if (param == null && data.pages.length) {\n                        return Promise.resolve(data);\n                    }\n                    const createQueryFnContext = ()=>{\n                        const queryFnContext2 = {\n                            client: context.client,\n                            queryKey: context.queryKey,\n                            pageParam: param,\n                            direction: previous ? \"backward\" : \"forward\",\n                            meta: context.options.meta\n                        };\n                        addSignalProperty(queryFnContext2);\n                        return queryFnContext2;\n                    };\n                    const queryFnContext = createQueryFnContext();\n                    const page = await queryFn(queryFnContext);\n                    const { maxPages } = context.options;\n                    const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n                    return {\n                        pages: addTo(data.pages, page, maxPages),\n                        pageParams: addTo(data.pageParams, param, maxPages)\n                    };\n                };\n                if (direction && oldPages.length) {\n                    const previous = direction === \"backward\";\n                    const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n                    const oldData = {\n                        pages: oldPages,\n                        pageParams: oldPageParams\n                    };\n                    const param = pageParamFn(options, oldData);\n                    result = await fetchPage(oldData, param, previous);\n                } else {\n                    const remainingPages = pages ?? oldPages.length;\n                    do {\n                        const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n                        if (currentPage > 0 && param == null) {\n                            break;\n                        }\n                        result = await fetchPage(result, param);\n                        currentPage++;\n                    }while (currentPage < remainingPages);\n                }\n                return result;\n            };\n            if (context.options.persister) {\n                context.fetchFn = ()=>{\n                    return context.options.persister?.(fetchFn, {\n                        client: context.client,\n                        queryKey: context.queryKey,\n                        meta: context.options.meta,\n                        signal: context.signal\n                    }, query);\n                };\n            } else {\n                context.fetchFn = fetchFn;\n            }\n        }\n    };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n    const lastIndex = pages.length - 1;\n    return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n    return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n    if (!data) return false;\n    return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data) != null;\n}\n //# sourceMappingURL=infiniteQueryBehavior.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #observers;\n    #mutationCache;\n    #retryer;\n    constructor(config){\n        super();\n        this.mutationId = config.mutationId;\n        this.#mutationCache = config.mutationCache;\n        this.#observers = [];\n        this.state = config.state || getDefaultState();\n        this.setOptions(config.options);\n        this.scheduleGc();\n    }\n    setOptions(options) {\n        this.options = options;\n        this.updateGcTime(this.options.gcTime);\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    addObserver(observer) {\n        if (!this.#observers.includes(observer)) {\n            this.#observers.push(observer);\n            this.clearGcTimeout();\n            this.#mutationCache.notify({\n                type: \"observerAdded\",\n                mutation: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        this.#observers = this.#observers.filter((x)=>x !== observer);\n        this.scheduleGc();\n        this.#mutationCache.notify({\n            type: \"observerRemoved\",\n            mutation: this,\n            observer\n        });\n    }\n    optionalRemove() {\n        if (!this.#observers.length) {\n            if (this.state.status === \"pending\") {\n                this.scheduleGc();\n            } else {\n                this.#mutationCache.remove(this);\n            }\n        }\n    }\n    continue() {\n        return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n        this.execute(this.state.variables);\n    }\n    async execute(variables) {\n        const onContinue = ()=>{\n            this.#dispatch({\n                type: \"continue\"\n            });\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n            fn: ()=>{\n                if (!this.options.mutationFn) {\n                    return Promise.reject(new Error(\"No mutationFn found\"));\n                }\n                return this.options.mutationFn(variables);\n            },\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue,\n            retry: this.options.retry ?? 0,\n            retryDelay: this.options.retryDelay,\n            networkMode: this.options.networkMode,\n            canRun: ()=>this.#mutationCache.canRun(this)\n        });\n        const restored = this.state.status === \"pending\";\n        const isPaused = !this.#retryer.canStart();\n        try {\n            if (restored) {\n                onContinue();\n            } else {\n                this.#dispatch({\n                    type: \"pending\",\n                    variables,\n                    isPaused\n                });\n                await this.#mutationCache.config.onMutate?.(variables, this);\n                const context = await this.options.onMutate?.(variables);\n                if (context !== this.state.context) {\n                    this.#dispatch({\n                        type: \"pending\",\n                        context,\n                        variables,\n                        isPaused\n                    });\n                }\n            }\n            const data = await this.#retryer.start();\n            await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n            await this.options.onSuccess?.(data, variables, this.state.context);\n            await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n            await this.options.onSettled?.(data, null, variables, this.state.context);\n            this.#dispatch({\n                type: \"success\",\n                data\n            });\n            return data;\n        } catch (error) {\n            try {\n                await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n                await this.options.onError?.(error, variables, this.state.context);\n                await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n                await this.options.onSettled?.(void 0, error, variables, this.state.context);\n                throw error;\n            } finally{\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n        } finally{\n            this.#mutationCache.runNext(this);\n        }\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        failureCount: action.failureCount,\n                        failureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        isPaused: true\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        isPaused: false\n                    };\n                case \"pending\":\n                    return {\n                        ...state,\n                        context: action.context,\n                        data: void 0,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        isPaused: action.isPaused,\n                        status: \"pending\",\n                        variables: action.variables,\n                        submittedAt: Date.now()\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        status: \"success\",\n                        isPaused: false\n                    };\n                case \"error\":\n                    return {\n                        ...state,\n                        data: void 0,\n                        error: action.error,\n                        failureCount: state.failureCount + 1,\n                        failureReason: action.error,\n                        isPaused: false,\n                        status: \"error\"\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#observers.forEach((observer)=>{\n                observer.onMutationUpdate(action);\n            });\n            this.#mutationCache.notify({\n                mutation: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction getDefaultState() {\n    return {\n        context: void 0,\n        data: void 0,\n        error: null,\n        failureCount: 0,\n        failureReason: null,\n        isPaused: false,\n        status: \"idle\",\n        variables: void 0,\n        submittedAt: 0\n    };\n}\n //# sourceMappingURL=mutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#mutations = /* @__PURE__ */ new Set();\n        this.#scopes = /* @__PURE__ */ new Map();\n        this.#mutationId = 0;\n    }\n    #mutations;\n    #scopes;\n    #mutationId;\n    build(client, options, state) {\n        const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n            mutationCache: this,\n            mutationId: ++this.#mutationId,\n            options: client.defaultMutationOptions(options),\n            state\n        });\n        this.add(mutation);\n        return mutation;\n    }\n    add(mutation) {\n        this.#mutations.add(mutation);\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const scopedMutations = this.#scopes.get(scope);\n            if (scopedMutations) {\n                scopedMutations.push(mutation);\n            } else {\n                this.#scopes.set(scope, [\n                    mutation\n                ]);\n            }\n        }\n        this.notify({\n            type: \"added\",\n            mutation\n        });\n    }\n    remove(mutation) {\n        if (this.#mutations.delete(mutation)) {\n            const scope = scopeFor(mutation);\n            if (typeof scope === \"string\") {\n                const scopedMutations = this.#scopes.get(scope);\n                if (scopedMutations) {\n                    if (scopedMutations.length > 1) {\n                        const index = scopedMutations.indexOf(mutation);\n                        if (index !== -1) {\n                            scopedMutations.splice(index, 1);\n                        }\n                    } else if (scopedMutations[0] === mutation) {\n                        this.#scopes.delete(scope);\n                    }\n                }\n            }\n        }\n        this.notify({\n            type: \"removed\",\n            mutation\n        });\n    }\n    canRun(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const mutationsWithSameScope = this.#scopes.get(scope);\n            const firstPendingMutation = mutationsWithSameScope?.find((m)=>m.state.status === \"pending\");\n            return !firstPendingMutation || firstPendingMutation === mutation;\n        } else {\n            return true;\n        }\n    }\n    runNext(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const foundMutation = this.#scopes.get(scope)?.find((m)=>m !== mutation && m.state.isPaused);\n            return foundMutation?.continue() ?? Promise.resolve();\n        } else {\n            return Promise.resolve();\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#mutations.forEach((mutation)=>{\n                this.notify({\n                    type: \"removed\",\n                    mutation\n                });\n            });\n            this.#mutations.clear();\n            this.#scopes.clear();\n        });\n    }\n    getAll() {\n        return Array.from(this.#mutations);\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation));\n    }\n    findAll(filters = {}) {\n        return this.getAll().filter((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    resumePausedMutations() {\n        const pausedMutations = this.getAll().filter((x)=>x.state.isPaused);\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>Promise.all(pausedMutations.map((mutation)=>mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))));\n    }\n};\nfunction scopeFor(mutation) {\n    return mutation.options.scope?.id;\n}\n //# sourceMappingURL=mutationCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = (cb)=>setTimeout(cb, 0);\nfunction createNotifyManager() {\n    let queue = [];\n    let transactions = 0;\n    let notifyFn = (callback)=>{\n        callback();\n    };\n    let batchNotifyFn = (callback)=>{\n        callback();\n    };\n    let scheduleFn = defaultScheduler;\n    const schedule = (callback)=>{\n        if (transactions) {\n            queue.push(callback);\n        } else {\n            scheduleFn(()=>{\n                notifyFn(callback);\n            });\n        }\n    };\n    const flush = ()=>{\n        const originalQueue = queue;\n        queue = [];\n        if (originalQueue.length) {\n            scheduleFn(()=>{\n                batchNotifyFn(()=>{\n                    originalQueue.forEach((callback)=>{\n                        notifyFn(callback);\n                    });\n                });\n            });\n        }\n    };\n    return {\n        batch: (callback)=>{\n            let result;\n            transactions++;\n            try {\n                result = callback();\n            } finally{\n                transactions--;\n                if (!transactions) {\n                    flush();\n                }\n            }\n            return result;\n        },\n        /**\n     * All calls to the wrapped function will be batched.\n     */ batchCalls: (callback)=>{\n            return (...args)=>{\n                schedule(()=>{\n                    callback(...args);\n                });\n            };\n        },\n        schedule,\n        /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */ setNotifyFunction: (fn)=>{\n            notifyFn = fn;\n        },\n        /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */ setBatchNotifyFunction: (fn)=>{\n            batchNotifyFn = fn;\n        },\n        setScheduler: (fn)=>{\n            scheduleFn = fn;\n        }\n    };\n}\nvar notifyManager = createNotifyManager();\n //# sourceMappingURL=notifyManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #online;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#online = true;\n        this.#setup = (onOnline)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const onlineListener = ()=>onOnline(true);\n                const offlineListener = ()=>onOnline(false);\n                window.addEventListener(\"online\", onlineListener, false);\n                window.addEventListener(\"offline\", offlineListener, false);\n                return ()=>{\n                    window.removeEventListener(\"online\", onlineListener);\n                    window.removeEventListener(\"offline\", offlineListener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup(this.setOnline.bind(this));\n    }\n    setOnline(online) {\n        const changed = this.#online !== online;\n        if (changed) {\n            this.#online = online;\n            this.listeners.forEach((listener)=>{\n                listener(online);\n            });\n        }\n    }\n    isOnline() {\n        return this.#online;\n    }\n};\nvar onlineManager = new OnlineManager();\n //# sourceMappingURL=onlineManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #initialState;\n    #revertState;\n    #cache;\n    #client;\n    #retryer;\n    #defaultOptions;\n    #abortSignalConsumed;\n    constructor(config){\n        super();\n        this.#abortSignalConsumed = false;\n        this.#defaultOptions = config.defaultOptions;\n        this.setOptions(config.options);\n        this.observers = [];\n        this.#client = config.client;\n        this.#cache = this.#client.getQueryCache();\n        this.queryKey = config.queryKey;\n        this.queryHash = config.queryHash;\n        this.#initialState = getDefaultState(this.options);\n        this.state = config.state ?? this.#initialState;\n        this.scheduleGc();\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    get promise() {\n        return this.#retryer?.promise;\n    }\n    setOptions(options) {\n        this.options = {\n            ...this.#defaultOptions,\n            ...options\n        };\n        this.updateGcTime(this.options.gcTime);\n    }\n    optionalRemove() {\n        if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n            this.#cache.remove(this);\n        }\n    }\n    setData(newData, options) {\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n        this.#dispatch({\n            data,\n            type: \"success\",\n            dataUpdatedAt: options?.updatedAt,\n            manual: options?.manual\n        });\n        return data;\n    }\n    setState(state, setStateOptions) {\n        this.#dispatch({\n            type: \"setState\",\n            state,\n            setStateOptions\n        });\n    }\n    cancel(options) {\n        const promise = this.#retryer?.promise;\n        this.#retryer?.cancel(options);\n        return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n    }\n    destroy() {\n        super.destroy();\n        this.cancel({\n            silent: true\n        });\n    }\n    reset() {\n        this.destroy();\n        this.setState(this.#initialState);\n    }\n    isActive() {\n        return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false);\n    }\n    isDisabled() {\n        if (this.getObserversCount() > 0) {\n            return !this.isActive();\n        }\n        return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n    }\n    isStatic() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\");\n        }\n        return false;\n    }\n    isStale() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>observer.getCurrentResult().isStale);\n        }\n        return this.state.data === void 0 || this.state.isInvalidated;\n    }\n    isStaleByTime(staleTime = 0) {\n        if (this.state.data === void 0) {\n            return true;\n        }\n        if (staleTime === \"static\") {\n            return false;\n        }\n        if (this.state.isInvalidated) {\n            return true;\n        }\n        return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n    }\n    onFocus() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnWindowFocus());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    onOnline() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnReconnect());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    addObserver(observer) {\n        if (!this.observers.includes(observer)) {\n            this.observers.push(observer);\n            this.clearGcTimeout();\n            this.#cache.notify({\n                type: \"observerAdded\",\n                query: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        if (this.observers.includes(observer)) {\n            this.observers = this.observers.filter((x)=>x !== observer);\n            if (!this.observers.length) {\n                if (this.#retryer) {\n                    if (this.#abortSignalConsumed) {\n                        this.#retryer.cancel({\n                            revert: true\n                        });\n                    } else {\n                        this.#retryer.cancelRetry();\n                    }\n                }\n                this.scheduleGc();\n            }\n            this.#cache.notify({\n                type: \"observerRemoved\",\n                query: this,\n                observer\n            });\n        }\n    }\n    getObserversCount() {\n        return this.observers.length;\n    }\n    invalidate() {\n        if (!this.state.isInvalidated) {\n            this.#dispatch({\n                type: \"invalidate\"\n            });\n        }\n    }\n    fetch(options, fetchOptions) {\n        if (this.state.fetchStatus !== \"idle\") {\n            if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n                this.cancel({\n                    silent: true\n                });\n            } else if (this.#retryer) {\n                this.#retryer.continueRetry();\n                return this.#retryer.promise;\n            }\n        }\n        if (options) {\n            this.setOptions(options);\n        }\n        if (!this.options.queryFn) {\n            const observer = this.observers.find((x)=>x.options.queryFn);\n            if (observer) {\n                this.setOptions(observer.options);\n            }\n        }\n        if (true) {\n            if (!Array.isArray(this.options.queryKey)) {\n                console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n            }\n        }\n        const abortController = new AbortController();\n        const addSignalProperty = (object)=>{\n            Object.defineProperty(object, \"signal\", {\n                enumerable: true,\n                get: ()=>{\n                    this.#abortSignalConsumed = true;\n                    return abortController.signal;\n                }\n            });\n        };\n        const fetchFn = ()=>{\n            const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n            const createQueryFnContext = ()=>{\n                const queryFnContext2 = {\n                    client: this.#client,\n                    queryKey: this.queryKey,\n                    meta: this.meta\n                };\n                addSignalProperty(queryFnContext2);\n                return queryFnContext2;\n            };\n            const queryFnContext = createQueryFnContext();\n            this.#abortSignalConsumed = false;\n            if (this.options.persister) {\n                return this.options.persister(queryFn, queryFnContext, this);\n            }\n            return queryFn(queryFnContext);\n        };\n        const createFetchContext = ()=>{\n            const context2 = {\n                fetchOptions,\n                options: this.options,\n                queryKey: this.queryKey,\n                client: this.#client,\n                state: this.state,\n                fetchFn\n            };\n            addSignalProperty(context2);\n            return context2;\n        };\n        const context = createFetchContext();\n        this.options.behavior?.onFetch(context, this);\n        this.#revertState = this.state;\n        if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n            this.#dispatch({\n                type: \"fetch\",\n                meta: context.fetchOptions?.meta\n            });\n        }\n        const onError = (error)=>{\n            if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n            if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n                this.#cache.config.onError?.(error, this);\n                this.#cache.config.onSettled?.(this.state.data, error, this);\n            }\n            this.scheduleGc();\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n            initialPromise: fetchOptions?.initialPromise,\n            fn: context.fetchFn,\n            abort: abortController.abort.bind(abortController),\n            onSuccess: (data)=>{\n                if (data === void 0) {\n                    if (true) {\n                        console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n                    }\n                    onError(new Error(`${this.queryHash} data is undefined`));\n                    return;\n                }\n                try {\n                    this.setData(data);\n                } catch (error) {\n                    onError(error);\n                    return;\n                }\n                this.#cache.config.onSuccess?.(data, this);\n                this.#cache.config.onSettled?.(data, this.state.error, this);\n                this.scheduleGc();\n            },\n            onError,\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue: ()=>{\n                this.#dispatch({\n                    type: \"continue\"\n                });\n            },\n            retry: context.options.retry,\n            retryDelay: context.options.retryDelay,\n            networkMode: context.options.networkMode,\n            canRun: ()=>true\n        });\n        return this.#retryer.start();\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        fetchFailureCount: action.failureCount,\n                        fetchFailureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        fetchStatus: \"paused\"\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        fetchStatus: \"fetching\"\n                    };\n                case \"fetch\":\n                    return {\n                        ...state,\n                        ...fetchState(state.data, this.options),\n                        fetchMeta: action.meta ?? null\n                    };\n                case \"success\":\n                    this.#revertState = void 0;\n                    return {\n                        ...state,\n                        data: action.data,\n                        dataUpdateCount: state.dataUpdateCount + 1,\n                        dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n                        error: null,\n                        isInvalidated: false,\n                        status: \"success\",\n                        ...!action.manual && {\n                            fetchStatus: \"idle\",\n                            fetchFailureCount: 0,\n                            fetchFailureReason: null\n                        }\n                    };\n                case \"error\":\n                    const error = action.error;\n                    if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n                        return {\n                            ...this.#revertState,\n                            fetchStatus: \"idle\"\n                        };\n                    }\n                    return {\n                        ...state,\n                        error,\n                        errorUpdateCount: state.errorUpdateCount + 1,\n                        errorUpdatedAt: Date.now(),\n                        fetchFailureCount: state.fetchFailureCount + 1,\n                        fetchFailureReason: error,\n                        fetchStatus: \"idle\",\n                        status: \"error\"\n                    };\n                case \"invalidate\":\n                    return {\n                        ...state,\n                        isInvalidated: true\n                    };\n                case \"setState\":\n                    return {\n                        ...state,\n                        ...action.state\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.observers.forEach((observer)=>{\n                observer.onQueryUpdate();\n            });\n            this.#cache.notify({\n                query: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction fetchState(data, options) {\n    return {\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n        ...data === void 0 && {\n            error: null,\n            status: \"pending\"\n        }\n    };\n}\nfunction getDefaultState(options) {\n    const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n    const hasData = data !== void 0;\n    const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    return {\n        data,\n        dataUpdateCount: 0,\n        dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n        error: null,\n        errorUpdateCount: 0,\n        errorUpdatedAt: 0,\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchMeta: null,\n        isInvalidated: false,\n        status: hasData ? \"success\" : \"pending\",\n        fetchStatus: \"idle\"\n    };\n}\n //# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3F1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLGVBQWU7QUFTSztBQUMrQjtBQUNzQjtBQUM5QjtBQUMzQyxJQUFJWSxRQUFRLGNBQWNELG9EQUFTQTtJQUNqQyxDQUFDRSxZQUFZLENBQUM7SUFDZCxDQUFDQyxXQUFXLENBQUM7SUFDYixDQUFDQyxLQUFLLENBQUM7SUFDUCxDQUFDQyxNQUFNLENBQUM7SUFDUixDQUFDQyxPQUFPLENBQUM7SUFDVCxDQUFDQyxjQUFjLENBQUM7SUFDaEIsQ0FBQ0MsbUJBQW1CLENBQUM7SUFDckJDLFlBQVlDLE1BQU0sQ0FBRTtRQUNsQixLQUFLO1FBQ0wsSUFBSSxDQUFDLENBQUNGLG1CQUFtQixHQUFHO1FBQzVCLElBQUksQ0FBQyxDQUFDRCxjQUFjLEdBQUdHLE9BQU9ILGNBQWM7UUFDNUMsSUFBSSxDQUFDSSxVQUFVLENBQUNELE9BQU9FLE9BQU87UUFDOUIsSUFBSSxDQUFDQyxTQUFTLEdBQUcsRUFBRTtRQUNuQixJQUFJLENBQUMsQ0FBQ1IsTUFBTSxHQUFHSyxPQUFPTCxNQUFNO1FBQzVCLElBQUksQ0FBQyxDQUFDRCxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUNDLE1BQU0sQ0FBQ1MsYUFBYTtRQUN4QyxJQUFJLENBQUNDLFFBQVEsR0FBR0wsT0FBT0ssUUFBUTtRQUMvQixJQUFJLENBQUNDLFNBQVMsR0FBR04sT0FBT00sU0FBUztRQUNqQyxJQUFJLENBQUMsQ0FBQ2QsWUFBWSxHQUFHZSxnQkFBZ0IsSUFBSSxDQUFDTCxPQUFPO1FBQ2pELElBQUksQ0FBQ00sS0FBSyxHQUFHUixPQUFPUSxLQUFLLElBQUksSUFBSSxDQUFDLENBQUNoQixZQUFZO1FBQy9DLElBQUksQ0FBQ2lCLFVBQVU7SUFDakI7SUFDQSxJQUFJQyxPQUFPO1FBQ1QsT0FBTyxJQUFJLENBQUNSLE9BQU8sQ0FBQ1EsSUFBSTtJQUMxQjtJQUNBLElBQUlDLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVlO0lBQ3hCO0lBQ0FWLFdBQVdDLE9BQU8sRUFBRTtRQUNsQixJQUFJLENBQUNBLE9BQU8sR0FBRztZQUFFLEdBQUcsSUFBSSxDQUFDLENBQUNMLGNBQWM7WUFBRSxHQUFHSyxPQUFPO1FBQUM7UUFDckQsSUFBSSxDQUFDVSxZQUFZLENBQUMsSUFBSSxDQUFDVixPQUFPLENBQUNXLE1BQU07SUFDdkM7SUFDQUMsaUJBQWlCO1FBQ2YsSUFBSSxDQUFDLElBQUksQ0FBQ1gsU0FBUyxDQUFDWSxNQUFNLElBQUksSUFBSSxDQUFDUCxLQUFLLENBQUNRLFdBQVcsS0FBSyxRQUFRO1lBQy9ELElBQUksQ0FBQyxDQUFDdEIsS0FBSyxDQUFDdUIsTUFBTSxDQUFDLElBQUk7UUFDekI7SUFDRjtJQUNBQyxRQUFRQyxPQUFPLEVBQUVqQixPQUFPLEVBQUU7UUFDeEIsTUFBTWtCLE9BQU92QyxzREFBV0EsQ0FBQyxJQUFJLENBQUMyQixLQUFLLENBQUNZLElBQUksRUFBRUQsU0FBUyxJQUFJLENBQUNqQixPQUFPO1FBQy9ELElBQUksQ0FBQyxDQUFDbUIsUUFBUSxDQUFDO1lBQ2JEO1lBQ0FFLE1BQU07WUFDTkMsZUFBZXJCLFNBQVNzQjtZQUN4QkMsUUFBUXZCLFNBQVN1QjtRQUNuQjtRQUNBLE9BQU9MO0lBQ1Q7SUFDQU0sU0FBU2xCLEtBQUssRUFBRW1CLGVBQWUsRUFBRTtRQUMvQixJQUFJLENBQUMsQ0FBQ04sUUFBUSxDQUFDO1lBQUVDLE1BQU07WUFBWWQ7WUFBT21CO1FBQWdCO0lBQzVEO0lBQ0FDLE9BQU8xQixPQUFPLEVBQUU7UUFDZCxNQUFNUyxVQUFVLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVlO1FBQy9CLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVnQyxPQUFPMUI7UUFDdEIsT0FBT1MsVUFBVUEsUUFBUWtCLElBQUksQ0FBQ2pELDJDQUFJQSxFQUFFa0QsS0FBSyxDQUFDbEQsMkNBQUlBLElBQUltRCxRQUFRQyxPQUFPO0lBQ25FO0lBQ0FDLFVBQVU7UUFDUixLQUFLLENBQUNBO1FBQ04sSUFBSSxDQUFDTCxNQUFNLENBQUM7WUFBRU0sUUFBUTtRQUFLO0lBQzdCO0lBQ0FDLFFBQVE7UUFDTixJQUFJLENBQUNGLE9BQU87UUFDWixJQUFJLENBQUNQLFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQ2xDLFlBQVk7SUFDbEM7SUFDQTRDLFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQ2pDLFNBQVMsQ0FBQ2tDLElBQUksQ0FDeEIsQ0FBQ0MsV0FBYXhELHlEQUFjQSxDQUFDd0QsU0FBU3BDLE9BQU8sQ0FBQ3FDLE9BQU8sRUFBRSxJQUFJLE1BQU07SUFFckU7SUFDQUMsYUFBYTtRQUNYLElBQUksSUFBSSxDQUFDQyxpQkFBaUIsS0FBSyxHQUFHO1lBQ2hDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLFFBQVE7UUFDdkI7UUFDQSxPQUFPLElBQUksQ0FBQ2xDLE9BQU8sQ0FBQ3dDLE9BQU8sS0FBSzFELGdEQUFTQSxJQUFJLElBQUksQ0FBQ3dCLEtBQUssQ0FBQ21DLGVBQWUsR0FBRyxJQUFJLENBQUNuQyxLQUFLLENBQUNvQyxnQkFBZ0IsS0FBSztJQUM1RztJQUNBQyxXQUFXO1FBQ1QsSUFBSSxJQUFJLENBQUNKLGlCQUFpQixLQUFLLEdBQUc7WUFDaEMsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNrQyxJQUFJLENBQ3hCLENBQUNDLFdBQWF2RCwyREFBZ0JBLENBQUN1RCxTQUFTcEMsT0FBTyxDQUFDNEMsU0FBUyxFQUFFLElBQUksTUFBTTtRQUV6RTtRQUNBLE9BQU87SUFDVDtJQUNBQyxVQUFVO1FBQ1IsSUFBSSxJQUFJLENBQUNOLGlCQUFpQixLQUFLLEdBQUc7WUFDaEMsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNrQyxJQUFJLENBQ3hCLENBQUNDLFdBQWFBLFNBQVNVLGdCQUFnQixHQUFHRCxPQUFPO1FBRXJEO1FBQ0EsT0FBTyxJQUFJLENBQUN2QyxLQUFLLENBQUNZLElBQUksS0FBSyxLQUFLLEtBQUssSUFBSSxDQUFDWixLQUFLLENBQUN5QyxhQUFhO0lBQy9EO0lBQ0FDLGNBQWNKLFlBQVksQ0FBQyxFQUFFO1FBQzNCLElBQUksSUFBSSxDQUFDdEMsS0FBSyxDQUFDWSxJQUFJLEtBQUssS0FBSyxHQUFHO1lBQzlCLE9BQU87UUFDVDtRQUNBLElBQUkwQixjQUFjLFVBQVU7WUFDMUIsT0FBTztRQUNUO1FBQ0EsSUFBSSxJQUFJLENBQUN0QyxLQUFLLENBQUN5QyxhQUFhLEVBQUU7WUFDNUIsT0FBTztRQUNUO1FBQ0EsT0FBTyxDQUFDaEUseURBQWNBLENBQUMsSUFBSSxDQUFDdUIsS0FBSyxDQUFDZSxhQUFhLEVBQUV1QjtJQUNuRDtJQUNBSyxVQUFVO1FBQ1IsTUFBTWIsV0FBVyxJQUFJLENBQUNuQyxTQUFTLENBQUNpRCxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRUMsd0JBQXdCO1FBQ3RFaEIsVUFBVWlCLFFBQVE7WUFBRUMsZUFBZTtRQUFNO1FBQ3pDLElBQUksQ0FBQyxDQUFDNUQsT0FBTyxFQUFFNkQ7SUFDakI7SUFDQUMsV0FBVztRQUNULE1BQU1wQixXQUFXLElBQUksQ0FBQ25DLFNBQVMsQ0FBQ2lELElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFTSxzQkFBc0I7UUFDcEVyQixVQUFVaUIsUUFBUTtZQUFFQyxlQUFlO1FBQU07UUFDekMsSUFBSSxDQUFDLENBQUM1RCxPQUFPLEVBQUU2RDtJQUNqQjtJQUNBRyxZQUFZdEIsUUFBUSxFQUFFO1FBQ3BCLElBQUksQ0FBQyxJQUFJLENBQUNuQyxTQUFTLENBQUMwRCxRQUFRLENBQUN2QixXQUFXO1lBQ3RDLElBQUksQ0FBQ25DLFNBQVMsQ0FBQzJELElBQUksQ0FBQ3hCO1lBQ3BCLElBQUksQ0FBQ3lCLGNBQWM7WUFDbkIsSUFBSSxDQUFDLENBQUNyRSxLQUFLLENBQUNzRSxNQUFNLENBQUM7Z0JBQUUxQyxNQUFNO2dCQUFpQjJDLE9BQU8sSUFBSTtnQkFBRTNCO1lBQVM7UUFDcEU7SUFDRjtJQUNBNEIsZUFBZTVCLFFBQVEsRUFBRTtRQUN2QixJQUFJLElBQUksQ0FBQ25DLFNBQVMsQ0FBQzBELFFBQVEsQ0FBQ3ZCLFdBQVc7WUFDckMsSUFBSSxDQUFDbkMsU0FBUyxHQUFHLElBQUksQ0FBQ0EsU0FBUyxDQUFDZ0UsTUFBTSxDQUFDLENBQUNkLElBQU1BLE1BQU1mO1lBQ3BELElBQUksQ0FBQyxJQUFJLENBQUNuQyxTQUFTLENBQUNZLE1BQU0sRUFBRTtnQkFDMUIsSUFBSSxJQUFJLENBQUMsQ0FBQ25CLE9BQU8sRUFBRTtvQkFDakIsSUFBSSxJQUFJLENBQUMsQ0FBQ0UsbUJBQW1CLEVBQUU7d0JBQzdCLElBQUksQ0FBQyxDQUFDRixPQUFPLENBQUNnQyxNQUFNLENBQUM7NEJBQUV3QyxRQUFRO3dCQUFLO29CQUN0QyxPQUFPO3dCQUNMLElBQUksQ0FBQyxDQUFDeEUsT0FBTyxDQUFDeUUsV0FBVztvQkFDM0I7Z0JBQ0Y7Z0JBQ0EsSUFBSSxDQUFDNUQsVUFBVTtZQUNqQjtZQUNBLElBQUksQ0FBQyxDQUFDZixLQUFLLENBQUNzRSxNQUFNLENBQUM7Z0JBQUUxQyxNQUFNO2dCQUFtQjJDLE9BQU8sSUFBSTtnQkFBRTNCO1lBQVM7UUFDdEU7SUFDRjtJQUNBRyxvQkFBb0I7UUFDbEIsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNZLE1BQU07SUFDOUI7SUFDQXVELGFBQWE7UUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDOUQsS0FBSyxDQUFDeUMsYUFBYSxFQUFFO1lBQzdCLElBQUksQ0FBQyxDQUFDNUIsUUFBUSxDQUFDO2dCQUFFQyxNQUFNO1lBQWE7UUFDdEM7SUFDRjtJQUNBaUQsTUFBTXJFLE9BQU8sRUFBRXNFLFlBQVksRUFBRTtRQUMzQixJQUFJLElBQUksQ0FBQ2hFLEtBQUssQ0FBQ1EsV0FBVyxLQUFLLFFBQVE7WUFDckMsSUFBSSxJQUFJLENBQUNSLEtBQUssQ0FBQ1ksSUFBSSxLQUFLLEtBQUssS0FBS29ELGNBQWNoQixlQUFlO2dCQUM3RCxJQUFJLENBQUM1QixNQUFNLENBQUM7b0JBQUVNLFFBQVE7Z0JBQUs7WUFDN0IsT0FBTyxJQUFJLElBQUksQ0FBQyxDQUFDdEMsT0FBTyxFQUFFO2dCQUN4QixJQUFJLENBQUMsQ0FBQ0EsT0FBTyxDQUFDNkUsYUFBYTtnQkFDM0IsT0FBTyxJQUFJLENBQUMsQ0FBQzdFLE9BQU8sQ0FBQ2UsT0FBTztZQUM5QjtRQUNGO1FBQ0EsSUFBSVQsU0FBUztZQUNYLElBQUksQ0FBQ0QsVUFBVSxDQUFDQztRQUNsQjtRQUNBLElBQUksQ0FBQyxJQUFJLENBQUNBLE9BQU8sQ0FBQ3dDLE9BQU8sRUFBRTtZQUN6QixNQUFNSixXQUFXLElBQUksQ0FBQ25DLFNBQVMsQ0FBQ2lELElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFbkQsT0FBTyxDQUFDd0MsT0FBTztZQUM3RCxJQUFJSixVQUFVO2dCQUNaLElBQUksQ0FBQ3JDLFVBQVUsQ0FBQ3FDLFNBQVNwQyxPQUFPO1lBQ2xDO1FBQ0Y7UUFDQSxJQUFJd0UsSUFBcUMsRUFBRTtZQUN6QyxJQUFJLENBQUNDLE1BQU1DLE9BQU8sQ0FBQyxJQUFJLENBQUMxRSxPQUFPLENBQUNHLFFBQVEsR0FBRztnQkFDekN3RSxRQUFRQyxLQUFLLENBQ1gsQ0FBQyxtSUFBbUksQ0FBQztZQUV6STtRQUNGO1FBQ0EsTUFBTUMsa0JBQWtCLElBQUlDO1FBQzVCLE1BQU1DLG9CQUFvQixDQUFDQztZQUN6QkMsT0FBT0MsY0FBYyxDQUFDRixRQUFRLFVBQVU7Z0JBQ3RDRyxZQUFZO2dCQUNaQyxLQUFLO29CQUNILElBQUksQ0FBQyxDQUFDeEYsbUJBQW1CLEdBQUc7b0JBQzVCLE9BQU9pRixnQkFBZ0JRLE1BQU07Z0JBQy9CO1lBQ0Y7UUFDRjtRQUNBLE1BQU1DLFVBQVU7WUFDZCxNQUFNOUMsVUFBVS9ELHdEQUFhQSxDQUFDLElBQUksQ0FBQ3VCLE9BQU8sRUFBRXNFO1lBQzVDLE1BQU1pQix1QkFBdUI7Z0JBQzNCLE1BQU1DLGtCQUFrQjtvQkFDdEIvRixRQUFRLElBQUksQ0FBQyxDQUFDQSxNQUFNO29CQUNwQlUsVUFBVSxJQUFJLENBQUNBLFFBQVE7b0JBQ3ZCSyxNQUFNLElBQUksQ0FBQ0EsSUFBSTtnQkFDakI7Z0JBQ0F1RSxrQkFBa0JTO2dCQUNsQixPQUFPQTtZQUNUO1lBQ0EsTUFBTUMsaUJBQWlCRjtZQUN2QixJQUFJLENBQUMsQ0FBQzNGLG1CQUFtQixHQUFHO1lBQzVCLElBQUksSUFBSSxDQUFDSSxPQUFPLENBQUMwRixTQUFTLEVBQUU7Z0JBQzFCLE9BQU8sSUFBSSxDQUFDMUYsT0FBTyxDQUFDMEYsU0FBUyxDQUMzQmxELFNBQ0FpRCxnQkFDQSxJQUFJO1lBRVI7WUFDQSxPQUFPakQsUUFBUWlEO1FBQ2pCO1FBQ0EsTUFBTUUscUJBQXFCO1lBQ3pCLE1BQU1DLFdBQVc7Z0JBQ2Z0QjtnQkFDQXRFLFNBQVMsSUFBSSxDQUFDQSxPQUFPO2dCQUNyQkcsVUFBVSxJQUFJLENBQUNBLFFBQVE7Z0JBQ3ZCVixRQUFRLElBQUksQ0FBQyxDQUFDQSxNQUFNO2dCQUNwQmEsT0FBTyxJQUFJLENBQUNBLEtBQUs7Z0JBQ2pCZ0Y7WUFDRjtZQUNBUCxrQkFBa0JhO1lBQ2xCLE9BQU9BO1FBQ1Q7UUFDQSxNQUFNQyxVQUFVRjtRQUNoQixJQUFJLENBQUMzRixPQUFPLENBQUM4RixRQUFRLEVBQUVDLFFBQVFGLFNBQVMsSUFBSTtRQUM1QyxJQUFJLENBQUMsQ0FBQ3RHLFdBQVcsR0FBRyxJQUFJLENBQUNlLEtBQUs7UUFDOUIsSUFBSSxJQUFJLENBQUNBLEtBQUssQ0FBQ1EsV0FBVyxLQUFLLFVBQVUsSUFBSSxDQUFDUixLQUFLLENBQUMwRixTQUFTLEtBQUtILFFBQVF2QixZQUFZLEVBQUU5RCxNQUFNO1lBQzVGLElBQUksQ0FBQyxDQUFDVyxRQUFRLENBQUM7Z0JBQUVDLE1BQU07Z0JBQVNaLE1BQU1xRixRQUFRdkIsWUFBWSxFQUFFOUQ7WUFBSztRQUNuRTtRQUNBLE1BQU15RixVQUFVLENBQUNyQjtZQUNmLElBQUksQ0FBRXpGLENBQUFBLDZEQUFnQkEsQ0FBQ3lGLFVBQVVBLE1BQU01QyxNQUFNLEdBQUc7Z0JBQzlDLElBQUksQ0FBQyxDQUFDYixRQUFRLENBQUM7b0JBQ2JDLE1BQU07b0JBQ053RDtnQkFDRjtZQUNGO1lBQ0EsSUFBSSxDQUFDekYsNkRBQWdCQSxDQUFDeUYsUUFBUTtnQkFDNUIsSUFBSSxDQUFDLENBQUNwRixLQUFLLENBQUNNLE1BQU0sQ0FBQ21HLE9BQU8sR0FDeEJyQixPQUNBLElBQUk7Z0JBRU4sSUFBSSxDQUFDLENBQUNwRixLQUFLLENBQUNNLE1BQU0sQ0FBQ29HLFNBQVMsR0FDMUIsSUFBSSxDQUFDNUYsS0FBSyxDQUFDWSxJQUFJLEVBQ2YwRCxPQUNBLElBQUk7WUFFUjtZQUNBLElBQUksQ0FBQ3JFLFVBQVU7UUFDakI7UUFDQSxJQUFJLENBQUMsQ0FBQ2IsT0FBTyxHQUFHUiwwREFBYUEsQ0FBQztZQUM1QmlILGdCQUFnQjdCLGNBQWM2QjtZQUM5QkMsSUFBSVAsUUFBUVAsT0FBTztZQUNuQmUsT0FBT3hCLGdCQUFnQndCLEtBQUssQ0FBQ0MsSUFBSSxDQUFDekI7WUFDbEMwQixXQUFXLENBQUNyRjtnQkFDVixJQUFJQSxTQUFTLEtBQUssR0FBRztvQkFDbkIsSUFBSXNELElBQXFDLEVBQUU7d0JBQ3pDRyxRQUFRQyxLQUFLLENBQ1gsQ0FBQyxzSUFBc0ksRUFBRSxJQUFJLENBQUN4RSxTQUFTLENBQUMsQ0FBQztvQkFFN0o7b0JBQ0E2RixRQUFRLElBQUlPLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQ3BHLFNBQVMsQ0FBQyxrQkFBa0IsQ0FBQztvQkFDdkQ7Z0JBQ0Y7Z0JBQ0EsSUFBSTtvQkFDRixJQUFJLENBQUNZLE9BQU8sQ0FBQ0U7Z0JBQ2YsRUFBRSxPQUFPMEQsT0FBTztvQkFDZHFCLFFBQVFyQjtvQkFDUjtnQkFDRjtnQkFDQSxJQUFJLENBQUMsQ0FBQ3BGLEtBQUssQ0FBQ00sTUFBTSxDQUFDeUcsU0FBUyxHQUFHckYsTUFBTSxJQUFJO2dCQUN6QyxJQUFJLENBQUMsQ0FBQzFCLEtBQUssQ0FBQ00sTUFBTSxDQUFDb0csU0FBUyxHQUMxQmhGLE1BQ0EsSUFBSSxDQUFDWixLQUFLLENBQUNzRSxLQUFLLEVBQ2hCLElBQUk7Z0JBRU4sSUFBSSxDQUFDckUsVUFBVTtZQUNqQjtZQUNBMEY7WUFDQVEsUUFBUSxDQUFDQyxjQUFjOUI7Z0JBQ3JCLElBQUksQ0FBQyxDQUFDekQsUUFBUSxDQUFDO29CQUFFQyxNQUFNO29CQUFVc0Y7b0JBQWM5QjtnQkFBTTtZQUN2RDtZQUNBK0IsU0FBUztnQkFDUCxJQUFJLENBQUMsQ0FBQ3hGLFFBQVEsQ0FBQztvQkFBRUMsTUFBTTtnQkFBUTtZQUNqQztZQUNBd0YsWUFBWTtnQkFDVixJQUFJLENBQUMsQ0FBQ3pGLFFBQVEsQ0FBQztvQkFBRUMsTUFBTTtnQkFBVztZQUNwQztZQUNBeUYsT0FBT2hCLFFBQVE3RixPQUFPLENBQUM2RyxLQUFLO1lBQzVCQyxZQUFZakIsUUFBUTdGLE9BQU8sQ0FBQzhHLFVBQVU7WUFDdENDLGFBQWFsQixRQUFRN0YsT0FBTyxDQUFDK0csV0FBVztZQUN4Q0MsUUFBUSxJQUFNO1FBQ2hCO1FBQ0EsT0FBTyxJQUFJLENBQUMsQ0FBQ3RILE9BQU8sQ0FBQ3VILEtBQUs7SUFDNUI7SUFDQSxDQUFDOUYsUUFBUSxDQUFDK0YsTUFBTTtRQUNkLE1BQU1DLFVBQVUsQ0FBQzdHO1lBQ2YsT0FBUTRHLE9BQU85RixJQUFJO2dCQUNqQixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR2QsS0FBSzt3QkFDUjhHLG1CQUFtQkYsT0FBT1IsWUFBWTt3QkFDdENXLG9CQUFvQkgsT0FBT3RDLEtBQUs7b0JBQ2xDO2dCQUNGLEtBQUs7b0JBQ0gsT0FBTzt3QkFDTCxHQUFHdEUsS0FBSzt3QkFDUlEsYUFBYTtvQkFDZjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR1IsS0FBSzt3QkFDUlEsYUFBYTtvQkFDZjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR1IsS0FBSzt3QkFDUixHQUFHZ0gsV0FBV2hILE1BQU1ZLElBQUksRUFBRSxJQUFJLENBQUNsQixPQUFPLENBQUM7d0JBQ3ZDZ0csV0FBV2tCLE9BQU8xRyxJQUFJLElBQUk7b0JBQzVCO2dCQUNGLEtBQUs7b0JBQ0gsSUFBSSxDQUFDLENBQUNqQixXQUFXLEdBQUcsS0FBSztvQkFDekIsT0FBTzt3QkFDTCxHQUFHZSxLQUFLO3dCQUNSWSxNQUFNZ0csT0FBT2hHLElBQUk7d0JBQ2pCdUIsaUJBQWlCbkMsTUFBTW1DLGVBQWUsR0FBRzt3QkFDekNwQixlQUFlNkYsT0FBTzdGLGFBQWEsSUFBSWtHLEtBQUtDLEdBQUc7d0JBQy9DNUMsT0FBTzt3QkFDUDdCLGVBQWU7d0JBQ2YwRSxRQUFRO3dCQUNSLEdBQUcsQ0FBQ1AsT0FBTzNGLE1BQU0sSUFBSTs0QkFDbkJULGFBQWE7NEJBQ2JzRyxtQkFBbUI7NEJBQ25CQyxvQkFBb0I7d0JBQ3RCLENBQUM7b0JBQ0g7Z0JBQ0YsS0FBSztvQkFDSCxNQUFNekMsUUFBUXNDLE9BQU90QyxLQUFLO29CQUMxQixJQUFJekYsNkRBQWdCQSxDQUFDeUYsVUFBVUEsTUFBTVYsTUFBTSxJQUFJLElBQUksQ0FBQyxDQUFDM0UsV0FBVyxFQUFFO3dCQUNoRSxPQUFPOzRCQUFFLEdBQUcsSUFBSSxDQUFDLENBQUNBLFdBQVc7NEJBQUV1QixhQUFhO3dCQUFPO29CQUNyRDtvQkFDQSxPQUFPO3dCQUNMLEdBQUdSLEtBQUs7d0JBQ1JzRTt3QkFDQWxDLGtCQUFrQnBDLE1BQU1vQyxnQkFBZ0IsR0FBRzt3QkFDM0NnRixnQkFBZ0JILEtBQUtDLEdBQUc7d0JBQ3hCSixtQkFBbUI5RyxNQUFNOEcsaUJBQWlCLEdBQUc7d0JBQzdDQyxvQkFBb0J6Qzt3QkFDcEI5RCxhQUFhO3dCQUNiMkcsUUFBUTtvQkFDVjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR25ILEtBQUs7d0JBQ1J5QyxlQUFlO29CQUNqQjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR3pDLEtBQUs7d0JBQ1IsR0FBRzRHLE9BQU81RyxLQUFLO29CQUNqQjtZQUNKO1FBQ0Y7UUFDQSxJQUFJLENBQUNBLEtBQUssR0FBRzZHLFFBQVEsSUFBSSxDQUFDN0csS0FBSztRQUMvQnRCLDREQUFhQSxDQUFDMkksS0FBSyxDQUFDO1lBQ2xCLElBQUksQ0FBQzFILFNBQVMsQ0FBQzJILE9BQU8sQ0FBQyxDQUFDeEY7Z0JBQ3RCQSxTQUFTeUYsYUFBYTtZQUN4QjtZQUNBLElBQUksQ0FBQyxDQUFDckksS0FBSyxDQUFDc0UsTUFBTSxDQUFDO2dCQUFFQyxPQUFPLElBQUk7Z0JBQUUzQyxNQUFNO2dCQUFXOEY7WUFBTztRQUM1RDtJQUNGO0FBQ0Y7QUFDQSxTQUFTSSxXQUFXcEcsSUFBSSxFQUFFbEIsT0FBTztJQUMvQixPQUFPO1FBQ0xvSCxtQkFBbUI7UUFDbkJDLG9CQUFvQjtRQUNwQnZHLGFBQWE3QixxREFBUUEsQ0FBQ2UsUUFBUStHLFdBQVcsSUFBSSxhQUFhO1FBQzFELEdBQUc3RixTQUFTLEtBQUssS0FBSztZQUNwQjBELE9BQU87WUFDUDZDLFFBQVE7UUFDVixDQUFDO0lBQ0g7QUFDRjtBQUNBLFNBQVNwSCxnQkFBZ0JMLE9BQU87SUFDOUIsTUFBTWtCLE9BQU8sT0FBT2xCLFFBQVE4SCxXQUFXLEtBQUssYUFBYTlILFFBQVE4SCxXQUFXLEtBQUs5SCxRQUFROEgsV0FBVztJQUNwRyxNQUFNQyxVQUFVN0csU0FBUyxLQUFLO0lBQzlCLE1BQU04Ryx1QkFBdUJELFVBQVUsT0FBTy9ILFFBQVFnSSxvQkFBb0IsS0FBSyxhQUFhaEksUUFBUWdJLG9CQUFvQixLQUFLaEksUUFBUWdJLG9CQUFvQixHQUFHO0lBQzVKLE9BQU87UUFDTDlHO1FBQ0F1QixpQkFBaUI7UUFDakJwQixlQUFlMEcsVUFBVUMsd0JBQXdCVCxLQUFLQyxHQUFHLEtBQUs7UUFDOUQ1QyxPQUFPO1FBQ1BsQyxrQkFBa0I7UUFDbEJnRixnQkFBZ0I7UUFDaEJOLG1CQUFtQjtRQUNuQkMsb0JBQW9CO1FBQ3BCckIsV0FBVztRQUNYakQsZUFBZTtRQUNmMEUsUUFBUU0sVUFBVSxZQUFZO1FBQzlCakgsYUFBYTtJQUNmO0FBQ0Y7QUFJRSxDQUNGLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlYXNlLWFkbWluLXBhbmVsLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeS5qcz8zYTVjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9xdWVyeS50c1xuaW1wb3J0IHtcbiAgZW5zdXJlUXVlcnlGbixcbiAgbm9vcCxcbiAgcmVwbGFjZURhdGEsXG4gIHJlc29sdmVFbmFibGVkLFxuICByZXNvbHZlU3RhbGVUaW1lLFxuICBza2lwVG9rZW4sXG4gIHRpbWVVbnRpbFN0YWxlXG59IGZyb20gXCIuL3V0aWxzLmpzXCI7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgY2FuRmV0Y2gsIGNyZWF0ZVJldHJ5ZXIsIGlzQ2FuY2VsbGVkRXJyb3IgfSBmcm9tIFwiLi9yZXRyeWVyLmpzXCI7XG5pbXBvcnQgeyBSZW1vdmFibGUgfSBmcm9tIFwiLi9yZW1vdmFibGUuanNcIjtcbnZhciBRdWVyeSA9IGNsYXNzIGV4dGVuZHMgUmVtb3ZhYmxlIHtcbiAgI2luaXRpYWxTdGF0ZTtcbiAgI3JldmVydFN0YXRlO1xuICAjY2FjaGU7XG4gICNjbGllbnQ7XG4gICNyZXRyeWVyO1xuICAjZGVmYXVsdE9wdGlvbnM7XG4gICNhYm9ydFNpZ25hbENvbnN1bWVkO1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSBmYWxzZTtcbiAgICB0aGlzLiNkZWZhdWx0T3B0aW9ucyA9IGNvbmZpZy5kZWZhdWx0T3B0aW9ucztcbiAgICB0aGlzLnNldE9wdGlvbnMoY29uZmlnLm9wdGlvbnMpO1xuICAgIHRoaXMub2JzZXJ2ZXJzID0gW107XG4gICAgdGhpcy4jY2xpZW50ID0gY29uZmlnLmNsaWVudDtcbiAgICB0aGlzLiNjYWNoZSA9IHRoaXMuI2NsaWVudC5nZXRRdWVyeUNhY2hlKCk7XG4gICAgdGhpcy5xdWVyeUtleSA9IGNvbmZpZy5xdWVyeUtleTtcbiAgICB0aGlzLnF1ZXJ5SGFzaCA9IGNvbmZpZy5xdWVyeUhhc2g7XG4gICAgdGhpcy4jaW5pdGlhbFN0YXRlID0gZ2V0RGVmYXVsdFN0YXRlKHRoaXMub3B0aW9ucyk7XG4gICAgdGhpcy5zdGF0ZSA9IGNvbmZpZy5zdGF0ZSA/PyB0aGlzLiNpbml0aWFsU3RhdGU7XG4gICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gIH1cbiAgZ2V0IG1ldGEoKSB7XG4gICAgcmV0dXJuIHRoaXMub3B0aW9ucy5tZXRhO1xuICB9XG4gIGdldCBwcm9taXNlKCkge1xuICAgIHJldHVybiB0aGlzLiNyZXRyeWVyPy5wcm9taXNlO1xuICB9XG4gIHNldE9wdGlvbnMob3B0aW9ucykge1xuICAgIHRoaXMub3B0aW9ucyA9IHsgLi4udGhpcy4jZGVmYXVsdE9wdGlvbnMsIC4uLm9wdGlvbnMgfTtcbiAgICB0aGlzLnVwZGF0ZUdjVGltZSh0aGlzLm9wdGlvbnMuZ2NUaW1lKTtcbiAgfVxuICBvcHRpb25hbFJlbW92ZSgpIHtcbiAgICBpZiAoIXRoaXMub2JzZXJ2ZXJzLmxlbmd0aCAmJiB0aGlzLnN0YXRlLmZldGNoU3RhdHVzID09PSBcImlkbGVcIikge1xuICAgICAgdGhpcy4jY2FjaGUucmVtb3ZlKHRoaXMpO1xuICAgIH1cbiAgfVxuICBzZXREYXRhKG5ld0RhdGEsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBkYXRhID0gcmVwbGFjZURhdGEodGhpcy5zdGF0ZS5kYXRhLCBuZXdEYXRhLCB0aGlzLm9wdGlvbnMpO1xuICAgIHRoaXMuI2Rpc3BhdGNoKHtcbiAgICAgIGRhdGEsXG4gICAgICB0eXBlOiBcInN1Y2Nlc3NcIixcbiAgICAgIGRhdGFVcGRhdGVkQXQ6IG9wdGlvbnM/LnVwZGF0ZWRBdCxcbiAgICAgIG1hbnVhbDogb3B0aW9ucz8ubWFudWFsXG4gICAgfSk7XG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cbiAgc2V0U3RhdGUoc3RhdGUsIHNldFN0YXRlT3B0aW9ucykge1xuICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJzZXRTdGF0ZVwiLCBzdGF0ZSwgc2V0U3RhdGVPcHRpb25zIH0pO1xuICB9XG4gIGNhbmNlbChvcHRpb25zKSB7XG4gICAgY29uc3QgcHJvbWlzZSA9IHRoaXMuI3JldHJ5ZXI/LnByb21pc2U7XG4gICAgdGhpcy4jcmV0cnllcj8uY2FuY2VsKG9wdGlvbnMpO1xuICAgIHJldHVybiBwcm9taXNlID8gcHJvbWlzZS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gIH1cbiAgZGVzdHJveSgpIHtcbiAgICBzdXBlci5kZXN0cm95KCk7XG4gICAgdGhpcy5jYW5jZWwoeyBzaWxlbnQ6IHRydWUgfSk7XG4gIH1cbiAgcmVzZXQoKSB7XG4gICAgdGhpcy5kZXN0cm95KCk7XG4gICAgdGhpcy5zZXRTdGF0ZSh0aGlzLiNpbml0aWFsU3RhdGUpO1xuICB9XG4gIGlzQWN0aXZlKCkge1xuICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5zb21lKFxuICAgICAgKG9ic2VydmVyKSA9PiByZXNvbHZlRW5hYmxlZChvYnNlcnZlci5vcHRpb25zLmVuYWJsZWQsIHRoaXMpICE9PSBmYWxzZVxuICAgICk7XG4gIH1cbiAgaXNEaXNhYmxlZCgpIHtcbiAgICBpZiAodGhpcy5nZXRPYnNlcnZlcnNDb3VudCgpID4gMCkge1xuICAgICAgcmV0dXJuICF0aGlzLmlzQWN0aXZlKCk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLm9wdGlvbnMucXVlcnlGbiA9PT0gc2tpcFRva2VuIHx8IHRoaXMuc3RhdGUuZGF0YVVwZGF0ZUNvdW50ICsgdGhpcy5zdGF0ZS5lcnJvclVwZGF0ZUNvdW50ID09PSAwO1xuICB9XG4gIGlzU3RhdGljKCkge1xuICAgIGlmICh0aGlzLmdldE9ic2VydmVyc0NvdW50KCkgPiAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMuc29tZShcbiAgICAgICAgKG9ic2VydmVyKSA9PiByZXNvbHZlU3RhbGVUaW1lKG9ic2VydmVyLm9wdGlvbnMuc3RhbGVUaW1lLCB0aGlzKSA9PT0gXCJzdGF0aWNcIlxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlzU3RhbGUoKSB7XG4gICAgaWYgKHRoaXMuZ2V0T2JzZXJ2ZXJzQ291bnQoKSA+IDApIHtcbiAgICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5zb21lKFxuICAgICAgICAob2JzZXJ2ZXIpID0+IG9ic2VydmVyLmdldEN1cnJlbnRSZXN1bHQoKS5pc1N0YWxlXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5zdGF0ZS5kYXRhID09PSB2b2lkIDAgfHwgdGhpcy5zdGF0ZS5pc0ludmFsaWRhdGVkO1xuICB9XG4gIGlzU3RhbGVCeVRpbWUoc3RhbGVUaW1lID0gMCkge1xuICAgIGlmICh0aGlzLnN0YXRlLmRhdGEgPT09IHZvaWQgMCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGlmIChzdGFsZVRpbWUgPT09IFwic3RhdGljXCIpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKHRoaXMuc3RhdGUuaXNJbnZhbGlkYXRlZCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiAhdGltZVVudGlsU3RhbGUodGhpcy5zdGF0ZS5kYXRhVXBkYXRlZEF0LCBzdGFsZVRpbWUpO1xuICB9XG4gIG9uRm9jdXMoKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKCh4KSA9PiB4LnNob3VsZEZldGNoT25XaW5kb3dGb2N1cygpKTtcbiAgICBvYnNlcnZlcj8ucmVmZXRjaCh7IGNhbmNlbFJlZmV0Y2g6IGZhbHNlIH0pO1xuICAgIHRoaXMuI3JldHJ5ZXI/LmNvbnRpbnVlKCk7XG4gIH1cbiAgb25PbmxpbmUoKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKCh4KSA9PiB4LnNob3VsZEZldGNoT25SZWNvbm5lY3QoKSk7XG4gICAgb2JzZXJ2ZXI/LnJlZmV0Y2goeyBjYW5jZWxSZWZldGNoOiBmYWxzZSB9KTtcbiAgICB0aGlzLiNyZXRyeWVyPy5jb250aW51ZSgpO1xuICB9XG4gIGFkZE9ic2VydmVyKG9ic2VydmVyKSB7XG4gICAgaWYgKCF0aGlzLm9ic2VydmVycy5pbmNsdWRlcyhvYnNlcnZlcikpIHtcbiAgICAgIHRoaXMub2JzZXJ2ZXJzLnB1c2gob2JzZXJ2ZXIpO1xuICAgICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgICAgdGhpcy4jY2FjaGUubm90aWZ5KHsgdHlwZTogXCJvYnNlcnZlckFkZGVkXCIsIHF1ZXJ5OiB0aGlzLCBvYnNlcnZlciB9KTtcbiAgICB9XG4gIH1cbiAgcmVtb3ZlT2JzZXJ2ZXIob2JzZXJ2ZXIpIHtcbiAgICBpZiAodGhpcy5vYnNlcnZlcnMuaW5jbHVkZXMob2JzZXJ2ZXIpKSB7XG4gICAgICB0aGlzLm9ic2VydmVycyA9IHRoaXMub2JzZXJ2ZXJzLmZpbHRlcigoeCkgPT4geCAhPT0gb2JzZXJ2ZXIpO1xuICAgICAgaWYgKCF0aGlzLm9ic2VydmVycy5sZW5ndGgpIHtcbiAgICAgICAgaWYgKHRoaXMuI3JldHJ5ZXIpIHtcbiAgICAgICAgICBpZiAodGhpcy4jYWJvcnRTaWduYWxDb25zdW1lZCkge1xuICAgICAgICAgICAgdGhpcy4jcmV0cnllci5jYW5jZWwoeyByZXZlcnQ6IHRydWUgfSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuI3JldHJ5ZXIuY2FuY2VsUmV0cnkoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgICB9XG4gICAgICB0aGlzLiNjYWNoZS5ub3RpZnkoeyB0eXBlOiBcIm9ic2VydmVyUmVtb3ZlZFwiLCBxdWVyeTogdGhpcywgb2JzZXJ2ZXIgfSk7XG4gICAgfVxuICB9XG4gIGdldE9ic2VydmVyc0NvdW50KCkge1xuICAgIHJldHVybiB0aGlzLm9ic2VydmVycy5sZW5ndGg7XG4gIH1cbiAgaW52YWxpZGF0ZSgpIHtcbiAgICBpZiAoIXRoaXMuc3RhdGUuaXNJbnZhbGlkYXRlZCkge1xuICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImludmFsaWRhdGVcIiB9KTtcbiAgICB9XG4gIH1cbiAgZmV0Y2gob3B0aW9ucywgZmV0Y2hPcHRpb25zKSB7XG4gICAgaWYgKHRoaXMuc3RhdGUuZmV0Y2hTdGF0dXMgIT09IFwiaWRsZVwiKSB7XG4gICAgICBpZiAodGhpcy5zdGF0ZS5kYXRhICE9PSB2b2lkIDAgJiYgZmV0Y2hPcHRpb25zPy5jYW5jZWxSZWZldGNoKSB7XG4gICAgICAgIHRoaXMuY2FuY2VsKHsgc2lsZW50OiB0cnVlIH0pO1xuICAgICAgfSBlbHNlIGlmICh0aGlzLiNyZXRyeWVyKSB7XG4gICAgICAgIHRoaXMuI3JldHJ5ZXIuY29udGludWVSZXRyeSgpO1xuICAgICAgICByZXR1cm4gdGhpcy4jcmV0cnllci5wcm9taXNlO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAob3B0aW9ucykge1xuICAgICAgdGhpcy5zZXRPcHRpb25zKG9wdGlvbnMpO1xuICAgIH1cbiAgICBpZiAoIXRoaXMub3B0aW9ucy5xdWVyeUZuKSB7XG4gICAgICBjb25zdCBvYnNlcnZlciA9IHRoaXMub2JzZXJ2ZXJzLmZpbmQoKHgpID0+IHgub3B0aW9ucy5xdWVyeUZuKTtcbiAgICAgIGlmIChvYnNlcnZlcikge1xuICAgICAgICB0aGlzLnNldE9wdGlvbnMob2JzZXJ2ZXIub3B0aW9ucyk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheSh0aGlzLm9wdGlvbnMucXVlcnlLZXkpKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgYEFzIG9mIHY0LCBxdWVyeUtleSBuZWVkcyB0byBiZSBhbiBBcnJheS4gSWYgeW91IGFyZSB1c2luZyBhIHN0cmluZyBsaWtlICdyZXBvRGF0YScsIHBsZWFzZSBjaGFuZ2UgaXQgdG8gYW4gQXJyYXksIGUuZy4gWydyZXBvRGF0YSddYFxuICAgICAgICApO1xuICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBhYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgYWRkU2lnbmFsUHJvcGVydHkgPSAob2JqZWN0KSA9PiB7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqZWN0LCBcInNpZ25hbFwiLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogKCkgPT4ge1xuICAgICAgICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSB0cnVlO1xuICAgICAgICAgIHJldHVybiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IGZldGNoRm4gPSAoKSA9PiB7XG4gICAgICBjb25zdCBxdWVyeUZuID0gZW5zdXJlUXVlcnlGbih0aGlzLm9wdGlvbnMsIGZldGNoT3B0aW9ucyk7XG4gICAgICBjb25zdCBjcmVhdGVRdWVyeUZuQ29udGV4dCA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgcXVlcnlGbkNvbnRleHQyID0ge1xuICAgICAgICAgIGNsaWVudDogdGhpcy4jY2xpZW50LFxuICAgICAgICAgIHF1ZXJ5S2V5OiB0aGlzLnF1ZXJ5S2V5LFxuICAgICAgICAgIG1ldGE6IHRoaXMubWV0YVxuICAgICAgICB9O1xuICAgICAgICBhZGRTaWduYWxQcm9wZXJ0eShxdWVyeUZuQ29udGV4dDIpO1xuICAgICAgICByZXR1cm4gcXVlcnlGbkNvbnRleHQyO1xuICAgICAgfTtcbiAgICAgIGNvbnN0IHF1ZXJ5Rm5Db250ZXh0ID0gY3JlYXRlUXVlcnlGbkNvbnRleHQoKTtcbiAgICAgIHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQgPSBmYWxzZTtcbiAgICAgIGlmICh0aGlzLm9wdGlvbnMucGVyc2lzdGVyKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm9wdGlvbnMucGVyc2lzdGVyKFxuICAgICAgICAgIHF1ZXJ5Rm4sXG4gICAgICAgICAgcXVlcnlGbkNvbnRleHQsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHF1ZXJ5Rm4ocXVlcnlGbkNvbnRleHQpO1xuICAgIH07XG4gICAgY29uc3QgY3JlYXRlRmV0Y2hDb250ZXh0ID0gKCkgPT4ge1xuICAgICAgY29uc3QgY29udGV4dDIgPSB7XG4gICAgICAgIGZldGNoT3B0aW9ucyxcbiAgICAgICAgb3B0aW9uczogdGhpcy5vcHRpb25zLFxuICAgICAgICBxdWVyeUtleTogdGhpcy5xdWVyeUtleSxcbiAgICAgICAgY2xpZW50OiB0aGlzLiNjbGllbnQsXG4gICAgICAgIHN0YXRlOiB0aGlzLnN0YXRlLFxuICAgICAgICBmZXRjaEZuXG4gICAgICB9O1xuICAgICAgYWRkU2lnbmFsUHJvcGVydHkoY29udGV4dDIpO1xuICAgICAgcmV0dXJuIGNvbnRleHQyO1xuICAgIH07XG4gICAgY29uc3QgY29udGV4dCA9IGNyZWF0ZUZldGNoQ29udGV4dCgpO1xuICAgIHRoaXMub3B0aW9ucy5iZWhhdmlvcj8ub25GZXRjaChjb250ZXh0LCB0aGlzKTtcbiAgICB0aGlzLiNyZXZlcnRTdGF0ZSA9IHRoaXMuc3RhdGU7XG4gICAgaWYgKHRoaXMuc3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwiaWRsZVwiIHx8IHRoaXMuc3RhdGUuZmV0Y2hNZXRhICE9PSBjb250ZXh0LmZldGNoT3B0aW9ucz8ubWV0YSkge1xuICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImZldGNoXCIsIG1ldGE6IGNvbnRleHQuZmV0Y2hPcHRpb25zPy5tZXRhIH0pO1xuICAgIH1cbiAgICBjb25zdCBvbkVycm9yID0gKGVycm9yKSA9PiB7XG4gICAgICBpZiAoIShpc0NhbmNlbGxlZEVycm9yKGVycm9yKSAmJiBlcnJvci5zaWxlbnQpKSB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHtcbiAgICAgICAgICB0eXBlOiBcImVycm9yXCIsXG4gICAgICAgICAgZXJyb3JcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBpZiAoIWlzQ2FuY2VsbGVkRXJyb3IoZXJyb3IpKSB7XG4gICAgICAgIHRoaXMuI2NhY2hlLmNvbmZpZy5vbkVycm9yPy4oXG4gICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgICB0aGlzLiNjYWNoZS5jb25maWcub25TZXR0bGVkPy4oXG4gICAgICAgICAgdGhpcy5zdGF0ZS5kYXRhLFxuICAgICAgICAgIGVycm9yLFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICAgIH07XG4gICAgdGhpcy4jcmV0cnllciA9IGNyZWF0ZVJldHJ5ZXIoe1xuICAgICAgaW5pdGlhbFByb21pc2U6IGZldGNoT3B0aW9ucz8uaW5pdGlhbFByb21pc2UsXG4gICAgICBmbjogY29udGV4dC5mZXRjaEZuLFxuICAgICAgYWJvcnQ6IGFib3J0Q29udHJvbGxlci5hYm9ydC5iaW5kKGFib3J0Q29udHJvbGxlciksXG4gICAgICBvblN1Y2Nlc3M6IChkYXRhKSA9PiB7XG4gICAgICAgIGlmIChkYXRhID09PSB2b2lkIDApIHtcbiAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBgUXVlcnkgZGF0YSBjYW5ub3QgYmUgdW5kZWZpbmVkLiBQbGVhc2UgbWFrZSBzdXJlIHRvIHJldHVybiBhIHZhbHVlIG90aGVyIHRoYW4gdW5kZWZpbmVkIGZyb20geW91ciBxdWVyeSBmdW5jdGlvbi4gQWZmZWN0ZWQgcXVlcnkga2V5OiAke3RoaXMucXVlcnlIYXNofWBcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICAgIG9uRXJyb3IobmV3IEVycm9yKGAke3RoaXMucXVlcnlIYXNofSBkYXRhIGlzIHVuZGVmaW5lZGApKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICB0aGlzLnNldERhdGEoZGF0YSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgb25FcnJvcihlcnJvcik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuI2NhY2hlLmNvbmZpZy5vblN1Y2Nlc3M/LihkYXRhLCB0aGlzKTtcbiAgICAgICAgdGhpcy4jY2FjaGUuY29uZmlnLm9uU2V0dGxlZD8uKFxuICAgICAgICAgIGRhdGEsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5lcnJvcixcbiAgICAgICAgICB0aGlzXG4gICAgICAgICk7XG4gICAgICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICAgICAgfSxcbiAgICAgIG9uRXJyb3IsXG4gICAgICBvbkZhaWw6IChmYWlsdXJlQ291bnQsIGVycm9yKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJmYWlsZWRcIiwgZmFpbHVyZUNvdW50LCBlcnJvciB9KTtcbiAgICAgIH0sXG4gICAgICBvblBhdXNlOiAoKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJwYXVzZVwiIH0pO1xuICAgICAgfSxcbiAgICAgIG9uQ29udGludWU6ICgpID0+IHtcbiAgICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImNvbnRpbnVlXCIgfSk7XG4gICAgICB9LFxuICAgICAgcmV0cnk6IGNvbnRleHQub3B0aW9ucy5yZXRyeSxcbiAgICAgIHJldHJ5RGVsYXk6IGNvbnRleHQub3B0aW9ucy5yZXRyeURlbGF5LFxuICAgICAgbmV0d29ya01vZGU6IGNvbnRleHQub3B0aW9ucy5uZXR3b3JrTW9kZSxcbiAgICAgIGNhblJ1bjogKCkgPT4gdHJ1ZVxuICAgIH0pO1xuICAgIHJldHVybiB0aGlzLiNyZXRyeWVyLnN0YXJ0KCk7XG4gIH1cbiAgI2Rpc3BhdGNoKGFjdGlvbikge1xuICAgIGNvbnN0IHJlZHVjZXIgPSAoc3RhdGUpID0+IHtcbiAgICAgIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICAgICAgY2FzZSBcImZhaWxlZFwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiBhY3Rpb24uZmFpbHVyZUNvdW50LFxuICAgICAgICAgICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBhY3Rpb24uZXJyb3JcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwicGF1c2VcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBmZXRjaFN0YXR1czogXCJwYXVzZWRcIlxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJjb250aW51ZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGZldGNoU3RhdHVzOiBcImZldGNoaW5nXCJcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiZmV0Y2hcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICAuLi5mZXRjaFN0YXRlKHN0YXRlLmRhdGEsIHRoaXMub3B0aW9ucyksXG4gICAgICAgICAgICBmZXRjaE1ldGE6IGFjdGlvbi5tZXRhID8/IG51bGxcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwic3VjY2Vzc1wiOlxuICAgICAgICAgIHRoaXMuI3JldmVydFN0YXRlID0gdm9pZCAwO1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGRhdGE6IGFjdGlvbi5kYXRhLFxuICAgICAgICAgICAgZGF0YVVwZGF0ZUNvdW50OiBzdGF0ZS5kYXRhVXBkYXRlQ291bnQgKyAxLFxuICAgICAgICAgICAgZGF0YVVwZGF0ZWRBdDogYWN0aW9uLmRhdGFVcGRhdGVkQXQgPz8gRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgICAgaXNJbnZhbGlkYXRlZDogZmFsc2UsXG4gICAgICAgICAgICBzdGF0dXM6IFwic3VjY2Vzc1wiLFxuICAgICAgICAgICAgLi4uIWFjdGlvbi5tYW51YWwgJiYge1xuICAgICAgICAgICAgICBmZXRjaFN0YXR1czogXCJpZGxlXCIsXG4gICAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgICAgICAgICAgICBmZXRjaEZhaWx1cmVSZWFzb246IG51bGxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgICBjb25zdCBlcnJvciA9IGFjdGlvbi5lcnJvcjtcbiAgICAgICAgICBpZiAoaXNDYW5jZWxsZWRFcnJvcihlcnJvcikgJiYgZXJyb3IucmV2ZXJ0ICYmIHRoaXMuI3JldmVydFN0YXRlKSB7XG4gICAgICAgICAgICByZXR1cm4geyAuLi50aGlzLiNyZXZlcnRTdGF0ZSwgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiIH07XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgZXJyb3JVcGRhdGVDb3VudDogc3RhdGUuZXJyb3JVcGRhdGVDb3VudCArIDEsXG4gICAgICAgICAgICBlcnJvclVwZGF0ZWRBdDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIGZldGNoRmFpbHVyZUNvdW50OiBzdGF0ZS5mZXRjaEZhaWx1cmVDb3VudCArIDEsXG4gICAgICAgICAgICBmZXRjaEZhaWx1cmVSZWFzb246IGVycm9yLFxuICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiLFxuICAgICAgICAgICAgc3RhdHVzOiBcImVycm9yXCJcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiaW52YWxpZGF0ZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGlzSW52YWxpZGF0ZWQ6IHRydWVcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwic2V0U3RhdGVcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICAuLi5hY3Rpb24uc3RhdGVcbiAgICAgICAgICB9O1xuICAgICAgfVxuICAgIH07XG4gICAgdGhpcy5zdGF0ZSA9IHJlZHVjZXIodGhpcy5zdGF0ZSk7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICB0aGlzLm9ic2VydmVycy5mb3JFYWNoKChvYnNlcnZlcikgPT4ge1xuICAgICAgICBvYnNlcnZlci5vblF1ZXJ5VXBkYXRlKCk7XG4gICAgICB9KTtcbiAgICAgIHRoaXMuI2NhY2hlLm5vdGlmeSh7IHF1ZXJ5OiB0aGlzLCB0eXBlOiBcInVwZGF0ZWRcIiwgYWN0aW9uIH0pO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gZmV0Y2hTdGF0ZShkYXRhLCBvcHRpb25zKSB7XG4gIHJldHVybiB7XG4gICAgZmV0Y2hGYWlsdXJlQ291bnQ6IDAsXG4gICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBudWxsLFxuICAgIGZldGNoU3RhdHVzOiBjYW5GZXRjaChvcHRpb25zLm5ldHdvcmtNb2RlKSA/IFwiZmV0Y2hpbmdcIiA6IFwicGF1c2VkXCIsXG4gICAgLi4uZGF0YSA9PT0gdm9pZCAwICYmIHtcbiAgICAgIGVycm9yOiBudWxsLFxuICAgICAgc3RhdHVzOiBcInBlbmRpbmdcIlxuICAgIH1cbiAgfTtcbn1cbmZ1bmN0aW9uIGdldERlZmF1bHRTdGF0ZShvcHRpb25zKSB7XG4gIGNvbnN0IGRhdGEgPSB0eXBlb2Ygb3B0aW9ucy5pbml0aWFsRGF0YSA9PT0gXCJmdW5jdGlvblwiID8gb3B0aW9ucy5pbml0aWFsRGF0YSgpIDogb3B0aW9ucy5pbml0aWFsRGF0YTtcbiAgY29uc3QgaGFzRGF0YSA9IGRhdGEgIT09IHZvaWQgMDtcbiAgY29uc3QgaW5pdGlhbERhdGFVcGRhdGVkQXQgPSBoYXNEYXRhID8gdHlwZW9mIG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQgPT09IFwiZnVuY3Rpb25cIiA/IG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQoKSA6IG9wdGlvbnMuaW5pdGlhbERhdGFVcGRhdGVkQXQgOiAwO1xuICByZXR1cm4ge1xuICAgIGRhdGEsXG4gICAgZGF0YVVwZGF0ZUNvdW50OiAwLFxuICAgIGRhdGFVcGRhdGVkQXQ6IGhhc0RhdGEgPyBpbml0aWFsRGF0YVVwZGF0ZWRBdCA/PyBEYXRlLm5vdygpIDogMCxcbiAgICBlcnJvcjogbnVsbCxcbiAgICBlcnJvclVwZGF0ZUNvdW50OiAwLFxuICAgIGVycm9yVXBkYXRlZEF0OiAwLFxuICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgIGZldGNoRmFpbHVyZVJlYXNvbjogbnVsbCxcbiAgICBmZXRjaE1ldGE6IG51bGwsXG4gICAgaXNJbnZhbGlkYXRlZDogZmFsc2UsXG4gICAgc3RhdHVzOiBoYXNEYXRhID8gXCJzdWNjZXNzXCIgOiBcInBlbmRpbmdcIixcbiAgICBmZXRjaFN0YXR1czogXCJpZGxlXCJcbiAgfTtcbn1cbmV4cG9ydCB7XG4gIFF1ZXJ5LFxuICBmZXRjaFN0YXRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cXVlcnkuanMubWFwIl0sIm5hbWVzIjpbImVuc3VyZVF1ZXJ5Rm4iLCJub29wIiwicmVwbGFjZURhdGEiLCJyZXNvbHZlRW5hYmxlZCIsInJlc29sdmVTdGFsZVRpbWUiLCJza2lwVG9rZW4iLCJ0aW1lVW50aWxTdGFsZSIsIm5vdGlmeU1hbmFnZXIiLCJjYW5GZXRjaCIsImNyZWF0ZVJldHJ5ZXIiLCJpc0NhbmNlbGxlZEVycm9yIiwiUmVtb3ZhYmxlIiwiUXVlcnkiLCJpbml0aWFsU3RhdGUiLCJyZXZlcnRTdGF0ZSIsImNhY2hlIiwiY2xpZW50IiwicmV0cnllciIsImRlZmF1bHRPcHRpb25zIiwiYWJvcnRTaWduYWxDb25zdW1lZCIsImNvbnN0cnVjdG9yIiwiY29uZmlnIiwic2V0T3B0aW9ucyIsIm9wdGlvbnMiLCJvYnNlcnZlcnMiLCJnZXRRdWVyeUNhY2hlIiwicXVlcnlLZXkiLCJxdWVyeUhhc2giLCJnZXREZWZhdWx0U3RhdGUiLCJzdGF0ZSIsInNjaGVkdWxlR2MiLCJtZXRhIiwicHJvbWlzZSIsInVwZGF0ZUdjVGltZSIsImdjVGltZSIsIm9wdGlvbmFsUmVtb3ZlIiwibGVuZ3RoIiwiZmV0Y2hTdGF0dXMiLCJyZW1vdmUiLCJzZXREYXRhIiwibmV3RGF0YSIsImRhdGEiLCJkaXNwYXRjaCIsInR5cGUiLCJkYXRhVXBkYXRlZEF0IiwidXBkYXRlZEF0IiwibWFudWFsIiwic2V0U3RhdGUiLCJzZXRTdGF0ZU9wdGlvbnMiLCJjYW5jZWwiLCJ0aGVuIiwiY2F0Y2giLCJQcm9taXNlIiwicmVzb2x2ZSIsImRlc3Ryb3kiLCJzaWxlbnQiLCJyZXNldCIsImlzQWN0aXZlIiwic29tZSIsIm9ic2VydmVyIiwiZW5hYmxlZCIsImlzRGlzYWJsZWQiLCJnZXRPYnNlcnZlcnNDb3VudCIsInF1ZXJ5Rm4iLCJkYXRhVXBkYXRlQ291bnQiLCJlcnJvclVwZGF0ZUNvdW50IiwiaXNTdGF0aWMiLCJzdGFsZVRpbWUiLCJpc1N0YWxlIiwiZ2V0Q3VycmVudFJlc3VsdCIsImlzSW52YWxpZGF0ZWQiLCJpc1N0YWxlQnlUaW1lIiwib25Gb2N1cyIsImZpbmQiLCJ4Iiwic2hvdWxkRmV0Y2hPbldpbmRvd0ZvY3VzIiwicmVmZXRjaCIsImNhbmNlbFJlZmV0Y2giLCJjb250aW51ZSIsIm9uT25saW5lIiwic2hvdWxkRmV0Y2hPblJlY29ubmVjdCIsImFkZE9ic2VydmVyIiwiaW5jbHVkZXMiLCJwdXNoIiwiY2xlYXJHY1RpbWVvdXQiLCJub3RpZnkiLCJxdWVyeSIsInJlbW92ZU9ic2VydmVyIiwiZmlsdGVyIiwicmV2ZXJ0IiwiY2FuY2VsUmV0cnkiLCJpbnZhbGlkYXRlIiwiZmV0Y2giLCJmZXRjaE9wdGlvbnMiLCJjb250aW51ZVJldHJ5IiwicHJvY2VzcyIsIkFycmF5IiwiaXNBcnJheSIsImNvbnNvbGUiLCJlcnJvciIsImFib3J0Q29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsImFkZFNpZ25hbFByb3BlcnR5Iiwib2JqZWN0IiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJlbnVtZXJhYmxlIiwiZ2V0Iiwic2lnbmFsIiwiZmV0Y2hGbiIsImNyZWF0ZVF1ZXJ5Rm5Db250ZXh0IiwicXVlcnlGbkNvbnRleHQyIiwicXVlcnlGbkNvbnRleHQiLCJwZXJzaXN0ZXIiLCJjcmVhdGVGZXRjaENvbnRleHQiLCJjb250ZXh0MiIsImNvbnRleHQiLCJiZWhhdmlvciIsIm9uRmV0Y2giLCJmZXRjaE1ldGEiLCJvbkVycm9yIiwib25TZXR0bGVkIiwiaW5pdGlhbFByb21pc2UiLCJmbiIsImFib3J0IiwiYmluZCIsIm9uU3VjY2VzcyIsIkVycm9yIiwib25GYWlsIiwiZmFpbHVyZUNvdW50Iiwib25QYXVzZSIsIm9uQ29udGludWUiLCJyZXRyeSIsInJldHJ5RGVsYXkiLCJuZXR3b3JrTW9kZSIsImNhblJ1biIsInN0YXJ0IiwiYWN0aW9uIiwicmVkdWNlciIsImZldGNoRmFpbHVyZUNvdW50IiwiZmV0Y2hGYWlsdXJlUmVhc29uIiwiZmV0Y2hTdGF0ZSIsIkRhdGUiLCJub3ciLCJzdGF0dXMiLCJlcnJvclVwZGF0ZWRBdCIsImJhdGNoIiwiZm9yRWFjaCIsIm9uUXVlcnlVcGRhdGUiLCJpbml0aWFsRGF0YSIsImhhc0RhdGEiLCJpbml0aWFsRGF0YVVwZGF0ZWRBdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#queries = /* @__PURE__ */ new Map();\n    }\n    #queries;\n    build(client, options, state) {\n        const queryKey = options.queryKey;\n        const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n        let query = this.get(queryHash);\n        if (!query) {\n            query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n                client,\n                queryKey,\n                queryHash,\n                options: client.defaultQueryOptions(options),\n                state,\n                defaultOptions: client.getQueryDefaults(queryKey)\n            });\n            this.add(query);\n        }\n        return query;\n    }\n    add(query) {\n        if (!this.#queries.has(query.queryHash)) {\n            this.#queries.set(query.queryHash, query);\n            this.notify({\n                type: \"added\",\n                query\n            });\n        }\n    }\n    remove(query) {\n        const queryInMap = this.#queries.get(query.queryHash);\n        if (queryInMap) {\n            query.destroy();\n            if (queryInMap === query) {\n                this.#queries.delete(query.queryHash);\n            }\n            this.notify({\n                type: \"removed\",\n                query\n            });\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                this.remove(query);\n            });\n        });\n    }\n    get(queryHash) {\n        return this.#queries.get(queryHash);\n    }\n    getAll() {\n        return [\n            ...this.#queries.values()\n        ];\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query));\n    }\n    findAll(filters = {}) {\n        const queries = this.getAll();\n        return Object.keys(filters).length > 0 ? queries.filter((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    onFocus() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onFocus();\n            });\n        });\n    }\n    onOnline() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onOnline();\n            });\n        });\n    }\n};\n //# sourceMappingURL=queryCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n    #queryCache;\n    #mutationCache;\n    #defaultOptions;\n    #queryDefaults;\n    #mutationDefaults;\n    #mountCount;\n    #unsubscribeFocus;\n    #unsubscribeOnline;\n    constructor(config = {}){\n        this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n        this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n        this.#defaultOptions = config.defaultOptions || {};\n        this.#queryDefaults = /* @__PURE__ */ new Map();\n        this.#mutationDefaults = /* @__PURE__ */ new Map();\n        this.#mountCount = 0;\n    }\n    mount() {\n        this.#mountCount++;\n        if (this.#mountCount !== 1) return;\n        this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused)=>{\n            if (focused) {\n                await this.resumePausedMutations();\n                this.#queryCache.onFocus();\n            }\n        });\n        this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online)=>{\n            if (online) {\n                await this.resumePausedMutations();\n                this.#queryCache.onOnline();\n            }\n        });\n    }\n    unmount() {\n        this.#mountCount--;\n        if (this.#mountCount !== 0) return;\n        this.#unsubscribeFocus?.();\n        this.#unsubscribeFocus = void 0;\n        this.#unsubscribeOnline?.();\n        this.#unsubscribeOnline = void 0;\n    }\n    isFetching(filters) {\n        return this.#queryCache.findAll({\n            ...filters,\n            fetchStatus: \"fetching\"\n        }).length;\n    }\n    isMutating(filters) {\n        return this.#mutationCache.findAll({\n            ...filters,\n            status: \"pending\"\n        }).length;\n    }\n    /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */ getQueryData(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state.data;\n    }\n    ensureQueryData(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        const query = this.#queryCache.build(this, defaultedOptions);\n        const cachedData = query.state.data;\n        if (cachedData === void 0) {\n            return this.fetchQuery(options);\n        }\n        if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n            void this.prefetchQuery(defaultedOptions);\n        }\n        return Promise.resolve(cachedData);\n    }\n    getQueriesData(filters) {\n        return this.#queryCache.findAll(filters).map(({ queryKey, state })=>{\n            const data = state.data;\n            return [\n                queryKey,\n                data\n            ];\n        });\n    }\n    setQueryData(queryKey, updater, options) {\n        const defaultedOptions = this.defaultQueryOptions({\n            queryKey\n        });\n        const query = this.#queryCache.get(defaultedOptions.queryHash);\n        const prevData = query?.state.data;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n        if (data === void 0) {\n            return void 0;\n        }\n        return this.#queryCache.build(this, defaultedOptions).setData(data, {\n            ...options,\n            manual: true\n        });\n    }\n    setQueriesData(filters, updater, options) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map(({ queryKey })=>[\n                    queryKey,\n                    this.setQueryData(queryKey, updater, options)\n                ]));\n    }\n    getQueryState(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state;\n    }\n    removeQueries(filters) {\n        const queryCache = this.#queryCache;\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                queryCache.remove(query);\n            });\n        });\n    }\n    resetQueries(filters, options) {\n        const queryCache = this.#queryCache;\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                query.reset();\n            });\n            return this.refetchQueries({\n                type: \"active\",\n                ...filters\n            }, options);\n        });\n    }\n    cancelQueries(filters, cancelOptions = {}) {\n        const defaultedCancelOptions = {\n            revert: true,\n            ...cancelOptions\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map((query)=>query.cancel(defaultedCancelOptions)));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    invalidateQueries(filters, options = {}) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            this.#queryCache.findAll(filters).forEach((query)=>{\n                query.invalidate();\n            });\n            if (filters?.refetchType === \"none\") {\n                return Promise.resolve();\n            }\n            return this.refetchQueries({\n                ...filters,\n                type: filters?.refetchType ?? filters?.type ?? \"active\"\n            }, options);\n        });\n    }\n    refetchQueries(filters, options = {}) {\n        const fetchOptions = {\n            ...options,\n            cancelRefetch: options.cancelRefetch ?? true\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).filter((query)=>!query.isDisabled() && !query.isStatic()).map((query)=>{\n                let promise = query.fetch(void 0, fetchOptions);\n                if (!fetchOptions.throwOnError) {\n                    promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n                }\n                return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n            }));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchQuery(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        if (defaultedOptions.retry === void 0) {\n            defaultedOptions.retry = false;\n        }\n        const query = this.#queryCache.build(this, defaultedOptions);\n        return query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n    }\n    prefetchQuery(options) {\n        return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchInfiniteQuery(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.fetchQuery(options);\n    }\n    prefetchInfiniteQuery(options) {\n        return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    ensureInfiniteQueryData(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.ensureQueryData(options);\n    }\n    resumePausedMutations() {\n        if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n            return this.#mutationCache.resumePausedMutations();\n        }\n        return Promise.resolve();\n    }\n    getQueryCache() {\n        return this.#queryCache;\n    }\n    getMutationCache() {\n        return this.#mutationCache;\n    }\n    getDefaultOptions() {\n        return this.#defaultOptions;\n    }\n    setDefaultOptions(options) {\n        this.#defaultOptions = options;\n    }\n    setQueryDefaults(queryKey, options) {\n        this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n            queryKey,\n            defaultOptions: options\n        });\n    }\n    getQueryDefaults(queryKey) {\n        const defaults = [\n            ...this.#queryDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    setMutationDefaults(mutationKey, options) {\n        this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n            mutationKey,\n            defaultOptions: options\n        });\n    }\n    getMutationDefaults(mutationKey) {\n        const defaults = [\n            ...this.#mutationDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    defaultQueryOptions(options) {\n        if (options._defaulted) {\n            return options;\n        }\n        const defaultedOptions = {\n            ...this.#defaultOptions.queries,\n            ...this.getQueryDefaults(options.queryKey),\n            ...options,\n            _defaulted: true\n        };\n        if (!defaultedOptions.queryHash) {\n            defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n        }\n        if (defaultedOptions.refetchOnReconnect === void 0) {\n            defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n        }\n        if (defaultedOptions.throwOnError === void 0) {\n            defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n        }\n        if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n            defaultedOptions.networkMode = \"offlineFirst\";\n        }\n        if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n            defaultedOptions.enabled = false;\n        }\n        return defaultedOptions;\n    }\n    defaultMutationOptions(options) {\n        if (options?._defaulted) {\n            return options;\n        }\n        return {\n            ...this.#defaultOptions.mutations,\n            ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n            ...options,\n            _defaulted: true\n        };\n    }\n    clear() {\n        this.#queryCache.clear();\n        this.#mutationCache.clear();\n    }\n};\n //# sourceMappingURL=queryClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3F1ZXJ5Q2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEscUJBQXFCO0FBU0Q7QUFDeUI7QUFDTTtBQUNGO0FBQ0U7QUFDQTtBQUNnQjtBQUNuRSxJQUFJYSxjQUFjO0lBQ2hCLENBQUNDLFVBQVUsQ0FBQztJQUNaLENBQUNDLGFBQWEsQ0FBQztJQUNmLENBQUNDLGNBQWMsQ0FBQztJQUNoQixDQUFDQyxhQUFhLENBQUM7SUFDZixDQUFDQyxnQkFBZ0IsQ0FBQztJQUNsQixDQUFDQyxVQUFVLENBQUM7SUFDWixDQUFDQyxnQkFBZ0IsQ0FBQztJQUNsQixDQUFDQyxpQkFBaUIsQ0FBQztJQUNuQkMsWUFBWUMsU0FBUyxDQUFDLENBQUMsQ0FBRTtRQUN2QixJQUFJLENBQUMsQ0FBQ1QsVUFBVSxHQUFHUyxPQUFPVCxVQUFVLElBQUksSUFBSVAsc0RBQVVBO1FBQ3RELElBQUksQ0FBQyxDQUFDUSxhQUFhLEdBQUdRLE9BQU9SLGFBQWEsSUFBSSxJQUFJUCw0REFBYUE7UUFDL0QsSUFBSSxDQUFDLENBQUNRLGNBQWMsR0FBR08sT0FBT1AsY0FBYyxJQUFJLENBQUM7UUFDakQsSUFBSSxDQUFDLENBQUNDLGFBQWEsR0FBRyxhQUFhLEdBQUcsSUFBSU87UUFDMUMsSUFBSSxDQUFDLENBQUNOLGdCQUFnQixHQUFHLGFBQWEsR0FBRyxJQUFJTTtRQUM3QyxJQUFJLENBQUMsQ0FBQ0wsVUFBVSxHQUFHO0lBQ3JCO0lBQ0FNLFFBQVE7UUFDTixJQUFJLENBQUMsQ0FBQ04sVUFBVTtRQUNoQixJQUFJLElBQUksQ0FBQyxDQUFDQSxVQUFVLEtBQUssR0FBRztRQUM1QixJQUFJLENBQUMsQ0FBQ0MsZ0JBQWdCLEdBQUdYLDBEQUFZQSxDQUFDaUIsU0FBUyxDQUFDLE9BQU9DO1lBQ3JELElBQUlBLFNBQVM7Z0JBQ1gsTUFBTSxJQUFJLENBQUNDLHFCQUFxQjtnQkFDaEMsSUFBSSxDQUFDLENBQUNkLFVBQVUsQ0FBQ2UsT0FBTztZQUMxQjtRQUNGO1FBQ0EsSUFBSSxDQUFDLENBQUNSLGlCQUFpQixHQUFHWCw0REFBYUEsQ0FBQ2dCLFNBQVMsQ0FBQyxPQUFPSTtZQUN2RCxJQUFJQSxRQUFRO2dCQUNWLE1BQU0sSUFBSSxDQUFDRixxQkFBcUI7Z0JBQ2hDLElBQUksQ0FBQyxDQUFDZCxVQUFVLENBQUNpQixRQUFRO1lBQzNCO1FBQ0Y7SUFDRjtJQUNBQyxVQUFVO1FBQ1IsSUFBSSxDQUFDLENBQUNiLFVBQVU7UUFDaEIsSUFBSSxJQUFJLENBQUMsQ0FBQ0EsVUFBVSxLQUFLLEdBQUc7UUFDNUIsSUFBSSxDQUFDLENBQUNDLGdCQUFnQjtRQUN0QixJQUFJLENBQUMsQ0FBQ0EsZ0JBQWdCLEdBQUcsS0FBSztRQUM5QixJQUFJLENBQUMsQ0FBQ0MsaUJBQWlCO1FBQ3ZCLElBQUksQ0FBQyxDQUFDQSxpQkFBaUIsR0FBRyxLQUFLO0lBQ2pDO0lBQ0FZLFdBQVdDLE9BQU8sRUFBRTtRQUNsQixPQUFPLElBQUksQ0FBQyxDQUFDcEIsVUFBVSxDQUFDcUIsT0FBTyxDQUFDO1lBQUUsR0FBR0QsT0FBTztZQUFFRSxhQUFhO1FBQVcsR0FBR0MsTUFBTTtJQUNqRjtJQUNBQyxXQUFXSixPQUFPLEVBQUU7UUFDbEIsT0FBTyxJQUFJLENBQUMsQ0FBQ25CLGFBQWEsQ0FBQ29CLE9BQU8sQ0FBQztZQUFFLEdBQUdELE9BQU87WUFBRUssUUFBUTtRQUFVLEdBQUdGLE1BQU07SUFDOUU7SUFDQTs7Ozs7O0dBTUMsR0FDREcsYUFBYUMsUUFBUSxFQUFFO1FBQ3JCLE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQztZQUFFRjtRQUFTO1FBQ3BELE9BQU8sSUFBSSxDQUFDLENBQUMzQixVQUFVLENBQUM4QixHQUFHLENBQUNGLFFBQVFHLFNBQVMsR0FBR0MsTUFBTUM7SUFDeEQ7SUFDQUMsZ0JBQWdCTixPQUFPLEVBQUU7UUFDdkIsTUFBTU8sbUJBQW1CLElBQUksQ0FBQ04sbUJBQW1CLENBQUNEO1FBQ2xELE1BQU1RLFFBQVEsSUFBSSxDQUFDLENBQUNwQyxVQUFVLENBQUNxQyxLQUFLLENBQUMsSUFBSSxFQUFFRjtRQUMzQyxNQUFNRyxhQUFhRixNQUFNSixLQUFLLENBQUNDLElBQUk7UUFDbkMsSUFBSUssZUFBZSxLQUFLLEdBQUc7WUFDekIsT0FBTyxJQUFJLENBQUNDLFVBQVUsQ0FBQ1g7UUFDekI7UUFDQSxJQUFJQSxRQUFRWSxpQkFBaUIsSUFBSUosTUFBTUssYUFBYSxDQUFDbEQsMkRBQWdCQSxDQUFDNEMsaUJBQWlCTyxTQUFTLEVBQUVOLFNBQVM7WUFDekcsS0FBSyxJQUFJLENBQUNPLGFBQWEsQ0FBQ1I7UUFDMUI7UUFDQSxPQUFPUyxRQUFRQyxPQUFPLENBQUNQO0lBQ3pCO0lBQ0FRLGVBQWUxQixPQUFPLEVBQUU7UUFDdEIsT0FBTyxJQUFJLENBQUMsQ0FBQ3BCLFVBQVUsQ0FBQ3FCLE9BQU8sQ0FBQ0QsU0FBUzJCLEdBQUcsQ0FBQyxDQUFDLEVBQUVwQixRQUFRLEVBQUVLLEtBQUssRUFBRTtZQUMvRCxNQUFNQyxPQUFPRCxNQUFNQyxJQUFJO1lBQ3ZCLE9BQU87Z0JBQUNOO2dCQUFVTTthQUFLO1FBQ3pCO0lBQ0Y7SUFDQWUsYUFBYXJCLFFBQVEsRUFBRXNCLE9BQU8sRUFBRXJCLE9BQU8sRUFBRTtRQUN2QyxNQUFNTyxtQkFBbUIsSUFBSSxDQUFDTixtQkFBbUIsQ0FBQztZQUFFRjtRQUFTO1FBQzdELE1BQU1TLFFBQVEsSUFBSSxDQUFDLENBQUNwQyxVQUFVLENBQUM4QixHQUFHLENBQ2hDSyxpQkFBaUJKLFNBQVM7UUFFNUIsTUFBTW1CLFdBQVdkLE9BQU9KLE1BQU1DO1FBQzlCLE1BQU1BLE9BQU8vQywyREFBZ0JBLENBQUMrRCxTQUFTQztRQUN2QyxJQUFJakIsU0FBUyxLQUFLLEdBQUc7WUFDbkIsT0FBTyxLQUFLO1FBQ2Q7UUFDQSxPQUFPLElBQUksQ0FBQyxDQUFDakMsVUFBVSxDQUFDcUMsS0FBSyxDQUFDLElBQUksRUFBRUYsa0JBQWtCZ0IsT0FBTyxDQUFDbEIsTUFBTTtZQUFFLEdBQUdMLE9BQU87WUFBRXdCLFFBQVE7UUFBSztJQUNqRztJQUNBQyxlQUFlakMsT0FBTyxFQUFFNkIsT0FBTyxFQUFFckIsT0FBTyxFQUFFO1FBQ3hDLE9BQU8vQiw0REFBYUEsQ0FBQ3lELEtBQUssQ0FDeEIsSUFBTSxJQUFJLENBQUMsQ0FBQ3RELFVBQVUsQ0FBQ3FCLE9BQU8sQ0FBQ0QsU0FBUzJCLEdBQUcsQ0FBQyxDQUFDLEVBQUVwQixRQUFRLEVBQUUsR0FBSztvQkFDNURBO29CQUNBLElBQUksQ0FBQ3FCLFlBQVksQ0FBQ3JCLFVBQVVzQixTQUFTckI7aUJBQ3RDO0lBRUw7SUFDQTJCLGNBQWM1QixRQUFRLEVBQUU7UUFDdEIsTUFBTUMsVUFBVSxJQUFJLENBQUNDLG1CQUFtQixDQUFDO1lBQUVGO1FBQVM7UUFDcEQsT0FBTyxJQUFJLENBQUMsQ0FBQzNCLFVBQVUsQ0FBQzhCLEdBQUcsQ0FDekJGLFFBQVFHLFNBQVMsR0FDaEJDO0lBQ0w7SUFDQXdCLGNBQWNwQyxPQUFPLEVBQUU7UUFDckIsTUFBTXBCLGFBQWEsSUFBSSxDQUFDLENBQUNBLFVBQVU7UUFDbkNILDREQUFhQSxDQUFDeUQsS0FBSyxDQUFDO1lBQ2xCdEQsV0FBV3FCLE9BQU8sQ0FBQ0QsU0FBU3FDLE9BQU8sQ0FBQyxDQUFDckI7Z0JBQ25DcEMsV0FBVzBELE1BQU0sQ0FBQ3RCO1lBQ3BCO1FBQ0Y7SUFDRjtJQUNBdUIsYUFBYXZDLE9BQU8sRUFBRVEsT0FBTyxFQUFFO1FBQzdCLE1BQU01QixhQUFhLElBQUksQ0FBQyxDQUFDQSxVQUFVO1FBQ25DLE9BQU9ILDREQUFhQSxDQUFDeUQsS0FBSyxDQUFDO1lBQ3pCdEQsV0FBV3FCLE9BQU8sQ0FBQ0QsU0FBU3FDLE9BQU8sQ0FBQyxDQUFDckI7Z0JBQ25DQSxNQUFNd0IsS0FBSztZQUNiO1lBQ0EsT0FBTyxJQUFJLENBQUNDLGNBQWMsQ0FDeEI7Z0JBQ0VDLE1BQU07Z0JBQ04sR0FBRzFDLE9BQU87WUFDWixHQUNBUTtRQUVKO0lBQ0Y7SUFDQW1DLGNBQWMzQyxPQUFPLEVBQUU0QyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUU7UUFDekMsTUFBTUMseUJBQXlCO1lBQUVDLFFBQVE7WUFBTSxHQUFHRixhQUFhO1FBQUM7UUFDaEUsTUFBTUcsV0FBV3RFLDREQUFhQSxDQUFDeUQsS0FBSyxDQUNsQyxJQUFNLElBQUksQ0FBQyxDQUFDdEQsVUFBVSxDQUFDcUIsT0FBTyxDQUFDRCxTQUFTMkIsR0FBRyxDQUFDLENBQUNYLFFBQVVBLE1BQU1nQyxNQUFNLENBQUNIO1FBRXRFLE9BQU9yQixRQUFReUIsR0FBRyxDQUFDRixVQUFVRyxJQUFJLENBQUNqRiwyQ0FBSUEsRUFBRWtGLEtBQUssQ0FBQ2xGLDJDQUFJQTtJQUNwRDtJQUNBbUYsa0JBQWtCcEQsT0FBTyxFQUFFUSxVQUFVLENBQUMsQ0FBQyxFQUFFO1FBQ3ZDLE9BQU8vQiw0REFBYUEsQ0FBQ3lELEtBQUssQ0FBQztZQUN6QixJQUFJLENBQUMsQ0FBQ3RELFVBQVUsQ0FBQ3FCLE9BQU8sQ0FBQ0QsU0FBU3FDLE9BQU8sQ0FBQyxDQUFDckI7Z0JBQ3pDQSxNQUFNcUMsVUFBVTtZQUNsQjtZQUNBLElBQUlyRCxTQUFTc0QsZ0JBQWdCLFFBQVE7Z0JBQ25DLE9BQU85QixRQUFRQyxPQUFPO1lBQ3hCO1lBQ0EsT0FBTyxJQUFJLENBQUNnQixjQUFjLENBQ3hCO2dCQUNFLEdBQUd6QyxPQUFPO2dCQUNWMEMsTUFBTTFDLFNBQVNzRCxlQUFldEQsU0FBUzBDLFFBQVE7WUFDakQsR0FDQWxDO1FBRUo7SUFDRjtJQUNBaUMsZUFBZXpDLE9BQU8sRUFBRVEsVUFBVSxDQUFDLENBQUMsRUFBRTtRQUNwQyxNQUFNK0MsZUFBZTtZQUNuQixHQUFHL0MsT0FBTztZQUNWZ0QsZUFBZWhELFFBQVFnRCxhQUFhLElBQUk7UUFDMUM7UUFDQSxNQUFNVCxXQUFXdEUsNERBQWFBLENBQUN5RCxLQUFLLENBQ2xDLElBQU0sSUFBSSxDQUFDLENBQUN0RCxVQUFVLENBQUNxQixPQUFPLENBQUNELFNBQVN5RCxNQUFNLENBQUMsQ0FBQ3pDLFFBQVUsQ0FBQ0EsTUFBTTBDLFVBQVUsTUFBTSxDQUFDMUMsTUFBTTJDLFFBQVEsSUFBSWhDLEdBQUcsQ0FBQyxDQUFDWDtnQkFDdkcsSUFBSTRDLFVBQVU1QyxNQUFNNkMsS0FBSyxDQUFDLEtBQUssR0FBR047Z0JBQ2xDLElBQUksQ0FBQ0EsYUFBYU8sWUFBWSxFQUFFO29CQUM5QkYsVUFBVUEsUUFBUVQsS0FBSyxDQUFDbEYsMkNBQUlBO2dCQUM5QjtnQkFDQSxPQUFPK0MsTUFBTUosS0FBSyxDQUFDVixXQUFXLEtBQUssV0FBV3NCLFFBQVFDLE9BQU8sS0FBS21DO1lBQ3BFO1FBRUYsT0FBT3BDLFFBQVF5QixHQUFHLENBQUNGLFVBQVVHLElBQUksQ0FBQ2pGLDJDQUFJQTtJQUN4QztJQUNBa0QsV0FBV1gsT0FBTyxFQUFFO1FBQ2xCLE1BQU1PLG1CQUFtQixJQUFJLENBQUNOLG1CQUFtQixDQUFDRDtRQUNsRCxJQUFJTyxpQkFBaUJnRCxLQUFLLEtBQUssS0FBSyxHQUFHO1lBQ3JDaEQsaUJBQWlCZ0QsS0FBSyxHQUFHO1FBQzNCO1FBQ0EsTUFBTS9DLFFBQVEsSUFBSSxDQUFDLENBQUNwQyxVQUFVLENBQUNxQyxLQUFLLENBQUMsSUFBSSxFQUFFRjtRQUMzQyxPQUFPQyxNQUFNSyxhQUFhLENBQ3hCbEQsMkRBQWdCQSxDQUFDNEMsaUJBQWlCTyxTQUFTLEVBQUVOLFVBQzNDQSxNQUFNNkMsS0FBSyxDQUFDOUMsb0JBQW9CUyxRQUFRQyxPQUFPLENBQUNULE1BQU1KLEtBQUssQ0FBQ0MsSUFBSTtJQUN0RTtJQUNBVSxjQUFjZixPQUFPLEVBQUU7UUFDckIsT0FBTyxJQUFJLENBQUNXLFVBQVUsQ0FBQ1gsU0FBUzBDLElBQUksQ0FBQ2pGLDJDQUFJQSxFQUFFa0YsS0FBSyxDQUFDbEYsMkNBQUlBO0lBQ3ZEO0lBQ0ErRixtQkFBbUJ4RCxPQUFPLEVBQUU7UUFDMUJBLFFBQVF5RCxRQUFRLEdBQUd2RixnRkFBcUJBLENBQUM4QixRQUFRMEQsS0FBSztRQUN0RCxPQUFPLElBQUksQ0FBQy9DLFVBQVUsQ0FBQ1g7SUFDekI7SUFDQTJELHNCQUFzQjNELE9BQU8sRUFBRTtRQUM3QixPQUFPLElBQUksQ0FBQ3dELGtCQUFrQixDQUFDeEQsU0FBUzBDLElBQUksQ0FBQ2pGLDJDQUFJQSxFQUFFa0YsS0FBSyxDQUFDbEYsMkNBQUlBO0lBQy9EO0lBQ0FtRyx3QkFBd0I1RCxPQUFPLEVBQUU7UUFDL0JBLFFBQVF5RCxRQUFRLEdBQUd2RixnRkFBcUJBLENBQUM4QixRQUFRMEQsS0FBSztRQUN0RCxPQUFPLElBQUksQ0FBQ3BELGVBQWUsQ0FBQ047SUFDOUI7SUFDQWQsd0JBQXdCO1FBQ3RCLElBQUlsQiw0REFBYUEsQ0FBQzZGLFFBQVEsSUFBSTtZQUM1QixPQUFPLElBQUksQ0FBQyxDQUFDeEYsYUFBYSxDQUFDYSxxQkFBcUI7UUFDbEQ7UUFDQSxPQUFPOEIsUUFBUUMsT0FBTztJQUN4QjtJQUNBNkMsZ0JBQWdCO1FBQ2QsT0FBTyxJQUFJLENBQUMsQ0FBQzFGLFVBQVU7SUFDekI7SUFDQTJGLG1CQUFtQjtRQUNqQixPQUFPLElBQUksQ0FBQyxDQUFDMUYsYUFBYTtJQUM1QjtJQUNBMkYsb0JBQW9CO1FBQ2xCLE9BQU8sSUFBSSxDQUFDLENBQUMxRixjQUFjO0lBQzdCO0lBQ0EyRixrQkFBa0JqRSxPQUFPLEVBQUU7UUFDekIsSUFBSSxDQUFDLENBQUMxQixjQUFjLEdBQUcwQjtJQUN6QjtJQUNBa0UsaUJBQWlCbkUsUUFBUSxFQUFFQyxPQUFPLEVBQUU7UUFDbEMsSUFBSSxDQUFDLENBQUN6QixhQUFhLENBQUM0RixHQUFHLENBQUM1RyxrREFBT0EsQ0FBQ3dDLFdBQVc7WUFDekNBO1lBQ0F6QixnQkFBZ0IwQjtRQUNsQjtJQUNGO0lBQ0FvRSxpQkFBaUJyRSxRQUFRLEVBQUU7UUFDekIsTUFBTXNFLFdBQVc7ZUFBSSxJQUFJLENBQUMsQ0FBQzlGLGFBQWEsQ0FBQytGLE1BQU07U0FBRztRQUNsRCxNQUFNQyxTQUFTLENBQUM7UUFDaEJGLFNBQVN4QyxPQUFPLENBQUMsQ0FBQzJDO1lBQ2hCLElBQUk5RywwREFBZUEsQ0FBQ3FDLFVBQVV5RSxhQUFhekUsUUFBUSxHQUFHO2dCQUNwRDBFLE9BQU9DLE1BQU0sQ0FBQ0gsUUFBUUMsYUFBYWxHLGNBQWM7WUFDbkQ7UUFDRjtRQUNBLE9BQU9pRztJQUNUO0lBQ0FJLG9CQUFvQkMsV0FBVyxFQUFFNUUsT0FBTyxFQUFFO1FBQ3hDLElBQUksQ0FBQyxDQUFDeEIsZ0JBQWdCLENBQUMyRixHQUFHLENBQUM1RyxrREFBT0EsQ0FBQ3FILGNBQWM7WUFDL0NBO1lBQ0F0RyxnQkFBZ0IwQjtRQUNsQjtJQUNGO0lBQ0E2RSxvQkFBb0JELFdBQVcsRUFBRTtRQUMvQixNQUFNUCxXQUFXO2VBQUksSUFBSSxDQUFDLENBQUM3RixnQkFBZ0IsQ0FBQzhGLE1BQU07U0FBRztRQUNyRCxNQUFNQyxTQUFTLENBQUM7UUFDaEJGLFNBQVN4QyxPQUFPLENBQUMsQ0FBQzJDO1lBQ2hCLElBQUk5RywwREFBZUEsQ0FBQ2tILGFBQWFKLGFBQWFJLFdBQVcsR0FBRztnQkFDMURILE9BQU9DLE1BQU0sQ0FBQ0gsUUFBUUMsYUFBYWxHLGNBQWM7WUFDbkQ7UUFDRjtRQUNBLE9BQU9pRztJQUNUO0lBQ0F0RSxvQkFBb0JELE9BQU8sRUFBRTtRQUMzQixJQUFJQSxRQUFROEUsVUFBVSxFQUFFO1lBQ3RCLE9BQU85RTtRQUNUO1FBQ0EsTUFBTU8sbUJBQW1CO1lBQ3ZCLEdBQUcsSUFBSSxDQUFDLENBQUNqQyxjQUFjLENBQUN5RyxPQUFPO1lBQy9CLEdBQUcsSUFBSSxDQUFDWCxnQkFBZ0IsQ0FBQ3BFLFFBQVFELFFBQVEsQ0FBQztZQUMxQyxHQUFHQyxPQUFPO1lBQ1Y4RSxZQUFZO1FBQ2Q7UUFDQSxJQUFJLENBQUN2RSxpQkFBaUJKLFNBQVMsRUFBRTtZQUMvQkksaUJBQWlCSixTQUFTLEdBQUczQyxnRUFBcUJBLENBQ2hEK0MsaUJBQWlCUixRQUFRLEVBQ3pCUTtRQUVKO1FBQ0EsSUFBSUEsaUJBQWlCeUUsa0JBQWtCLEtBQUssS0FBSyxHQUFHO1lBQ2xEekUsaUJBQWlCeUUsa0JBQWtCLEdBQUd6RSxpQkFBaUIwRSxXQUFXLEtBQUs7UUFDekU7UUFDQSxJQUFJMUUsaUJBQWlCK0MsWUFBWSxLQUFLLEtBQUssR0FBRztZQUM1Qy9DLGlCQUFpQitDLFlBQVksR0FBRyxDQUFDLENBQUMvQyxpQkFBaUIyRSxRQUFRO1FBQzdEO1FBQ0EsSUFBSSxDQUFDM0UsaUJBQWlCMEUsV0FBVyxJQUFJMUUsaUJBQWlCNEUsU0FBUyxFQUFFO1lBQy9ENUUsaUJBQWlCMEUsV0FBVyxHQUFHO1FBQ2pDO1FBQ0EsSUFBSTFFLGlCQUFpQjZFLE9BQU8sS0FBS3hILGdEQUFTQSxFQUFFO1lBQzFDMkMsaUJBQWlCOEUsT0FBTyxHQUFHO1FBQzdCO1FBQ0EsT0FBTzlFO0lBQ1Q7SUFDQStFLHVCQUF1QnRGLE9BQU8sRUFBRTtRQUM5QixJQUFJQSxTQUFTOEUsWUFBWTtZQUN2QixPQUFPOUU7UUFDVDtRQUNBLE9BQU87WUFDTCxHQUFHLElBQUksQ0FBQyxDQUFDMUIsY0FBYyxDQUFDaUgsU0FBUztZQUNqQyxHQUFHdkYsU0FBUzRFLGVBQWUsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQzdFLFFBQVE0RSxXQUFXLENBQUM7WUFDeEUsR0FBRzVFLE9BQU87WUFDVjhFLFlBQVk7UUFDZDtJQUNGO0lBQ0FVLFFBQVE7UUFDTixJQUFJLENBQUMsQ0FBQ3BILFVBQVUsQ0FBQ29ILEtBQUs7UUFDdEIsSUFBSSxDQUFDLENBQUNuSCxhQUFhLENBQUNtSCxLQUFLO0lBQzNCO0FBQ0Y7QUFHRSxDQUNGLHVDQUF1QyIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlYXNlLWFkbWluLXBhbmVsLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9xdWVyeUNsaWVudC5qcz8wYmE2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9xdWVyeUNsaWVudC50c1xuaW1wb3J0IHtcbiAgZnVuY3Rpb25hbFVwZGF0ZSxcbiAgaGFzaEtleSxcbiAgaGFzaFF1ZXJ5S2V5QnlPcHRpb25zLFxuICBub29wLFxuICBwYXJ0aWFsTWF0Y2hLZXksXG4gIHJlc29sdmVTdGFsZVRpbWUsXG4gIHNraXBUb2tlblxufSBmcm9tIFwiLi91dGlscy5qc1wiO1xuaW1wb3J0IHsgUXVlcnlDYWNoZSB9IGZyb20gXCIuL3F1ZXJ5Q2FjaGUuanNcIjtcbmltcG9ydCB7IE11dGF0aW9uQ2FjaGUgfSBmcm9tIFwiLi9tdXRhdGlvbkNhY2hlLmpzXCI7XG5pbXBvcnQgeyBmb2N1c01hbmFnZXIgfSBmcm9tIFwiLi9mb2N1c01hbmFnZXIuanNcIjtcbmltcG9ydCB7IG9ubGluZU1hbmFnZXIgfSBmcm9tIFwiLi9vbmxpbmVNYW5hZ2VyLmpzXCI7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgaW5maW5pdGVRdWVyeUJlaGF2aW9yIH0gZnJvbSBcIi4vaW5maW5pdGVRdWVyeUJlaGF2aW9yLmpzXCI7XG52YXIgUXVlcnlDbGllbnQgPSBjbGFzcyB7XG4gICNxdWVyeUNhY2hlO1xuICAjbXV0YXRpb25DYWNoZTtcbiAgI2RlZmF1bHRPcHRpb25zO1xuICAjcXVlcnlEZWZhdWx0cztcbiAgI211dGF0aW9uRGVmYXVsdHM7XG4gICNtb3VudENvdW50O1xuICAjdW5zdWJzY3JpYmVGb2N1cztcbiAgI3Vuc3Vic2NyaWJlT25saW5lO1xuICBjb25zdHJ1Y3Rvcihjb25maWcgPSB7fSkge1xuICAgIHRoaXMuI3F1ZXJ5Q2FjaGUgPSBjb25maWcucXVlcnlDYWNoZSB8fCBuZXcgUXVlcnlDYWNoZSgpO1xuICAgIHRoaXMuI211dGF0aW9uQ2FjaGUgPSBjb25maWcubXV0YXRpb25DYWNoZSB8fCBuZXcgTXV0YXRpb25DYWNoZSgpO1xuICAgIHRoaXMuI2RlZmF1bHRPcHRpb25zID0gY29uZmlnLmRlZmF1bHRPcHRpb25zIHx8IHt9O1xuICAgIHRoaXMuI3F1ZXJ5RGVmYXVsdHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMuI211dGF0aW9uRGVmYXVsdHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMuI21vdW50Q291bnQgPSAwO1xuICB9XG4gIG1vdW50KCkge1xuICAgIHRoaXMuI21vdW50Q291bnQrKztcbiAgICBpZiAodGhpcy4jbW91bnRDb3VudCAhPT0gMSkgcmV0dXJuO1xuICAgIHRoaXMuI3Vuc3Vic2NyaWJlRm9jdXMgPSBmb2N1c01hbmFnZXIuc3Vic2NyaWJlKGFzeW5jIChmb2N1c2VkKSA9PiB7XG4gICAgICBpZiAoZm9jdXNlZCkge1xuICAgICAgICBhd2FpdCB0aGlzLnJlc3VtZVBhdXNlZE11dGF0aW9ucygpO1xuICAgICAgICB0aGlzLiNxdWVyeUNhY2hlLm9uRm9jdXMoKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICB0aGlzLiN1bnN1YnNjcmliZU9ubGluZSA9IG9ubGluZU1hbmFnZXIuc3Vic2NyaWJlKGFzeW5jIChvbmxpbmUpID0+IHtcbiAgICAgIGlmIChvbmxpbmUpIHtcbiAgICAgICAgYXdhaXQgdGhpcy5yZXN1bWVQYXVzZWRNdXRhdGlvbnMoKTtcbiAgICAgICAgdGhpcy4jcXVlcnlDYWNoZS5vbk9ubGluZSgpO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIHVubW91bnQoKSB7XG4gICAgdGhpcy4jbW91bnRDb3VudC0tO1xuICAgIGlmICh0aGlzLiNtb3VudENvdW50ICE9PSAwKSByZXR1cm47XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVGb2N1cz8uKCk7XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVGb2N1cyA9IHZvaWQgMDtcbiAgICB0aGlzLiN1bnN1YnNjcmliZU9ubGluZT8uKCk7XG4gICAgdGhpcy4jdW5zdWJzY3JpYmVPbmxpbmUgPSB2b2lkIDA7XG4gIH1cbiAgaXNGZXRjaGluZyhmaWx0ZXJzKSB7XG4gICAgcmV0dXJuIHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbCh7IC4uLmZpbHRlcnMsIGZldGNoU3RhdHVzOiBcImZldGNoaW5nXCIgfSkubGVuZ3RoO1xuICB9XG4gIGlzTXV0YXRpbmcoZmlsdGVycykge1xuICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlLmZpbmRBbGwoeyAuLi5maWx0ZXJzLCBzdGF0dXM6IFwicGVuZGluZ1wiIH0pLmxlbmd0aDtcbiAgfVxuICAvKipcbiAgICogSW1wZXJhdGl2ZSAobm9uLXJlYWN0aXZlKSB3YXkgdG8gcmV0cmlldmUgZGF0YSBmb3IgYSBRdWVyeUtleS5cbiAgICogU2hvdWxkIG9ubHkgYmUgdXNlZCBpbiBjYWxsYmFja3Mgb3IgZnVuY3Rpb25zIHdoZXJlIHJlYWRpbmcgdGhlIGxhdGVzdCBkYXRhIGlzIG5lY2Vzc2FyeSwgZS5nLiBmb3Igb3B0aW1pc3RpYyB1cGRhdGVzLlxuICAgKlxuICAgKiBIaW50OiBEbyBub3QgdXNlIHRoaXMgZnVuY3Rpb24gaW5zaWRlIGEgY29tcG9uZW50LCBiZWNhdXNlIGl0IHdvbid0IHJlY2VpdmUgdXBkYXRlcy5cbiAgICogVXNlIGB1c2VRdWVyeWAgdG8gY3JlYXRlIGEgYFF1ZXJ5T2JzZXJ2ZXJgIHRoYXQgc3Vic2NyaWJlcyB0byBjaGFuZ2VzLlxuICAgKi9cbiAgZ2V0UXVlcnlEYXRhKHF1ZXJ5S2V5KSB7XG4gICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMuZGVmYXVsdFF1ZXJ5T3B0aW9ucyh7IHF1ZXJ5S2V5IH0pO1xuICAgIHJldHVybiB0aGlzLiNxdWVyeUNhY2hlLmdldChvcHRpb25zLnF1ZXJ5SGFzaCk/LnN0YXRlLmRhdGE7XG4gIH1cbiAgZW5zdXJlUXVlcnlEYXRhKG9wdGlvbnMpIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRPcHRpb25zID0gdGhpcy5kZWZhdWx0UXVlcnlPcHRpb25zKG9wdGlvbnMpO1xuICAgIGNvbnN0IHF1ZXJ5ID0gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICBjb25zdCBjYWNoZWREYXRhID0gcXVlcnkuc3RhdGUuZGF0YTtcbiAgICBpZiAoY2FjaGVkRGF0YSA9PT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5mZXRjaFF1ZXJ5KG9wdGlvbnMpO1xuICAgIH1cbiAgICBpZiAob3B0aW9ucy5yZXZhbGlkYXRlSWZTdGFsZSAmJiBxdWVyeS5pc1N0YWxlQnlUaW1lKHJlc29sdmVTdGFsZVRpbWUoZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUsIHF1ZXJ5KSkpIHtcbiAgICAgIHZvaWQgdGhpcy5wcmVmZXRjaFF1ZXJ5KGRlZmF1bHRlZE9wdGlvbnMpO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKGNhY2hlZERhdGEpO1xuICB9XG4gIGdldFF1ZXJpZXNEYXRhKGZpbHRlcnMpIHtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLm1hcCgoeyBxdWVyeUtleSwgc3RhdGUgfSkgPT4ge1xuICAgICAgY29uc3QgZGF0YSA9IHN0YXRlLmRhdGE7XG4gICAgICByZXR1cm4gW3F1ZXJ5S2V5LCBkYXRhXTtcbiAgICB9KTtcbiAgfVxuICBzZXRRdWVyeURhdGEocXVlcnlLZXksIHVwZGF0ZXIsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRPcHRpb25zID0gdGhpcy5kZWZhdWx0UXVlcnlPcHRpb25zKHsgcXVlcnlLZXkgfSk7XG4gICAgY29uc3QgcXVlcnkgPSB0aGlzLiNxdWVyeUNhY2hlLmdldChcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNoXG4gICAgKTtcbiAgICBjb25zdCBwcmV2RGF0YSA9IHF1ZXJ5Py5zdGF0ZS5kYXRhO1xuICAgIGNvbnN0IGRhdGEgPSBmdW5jdGlvbmFsVXBkYXRlKHVwZGF0ZXIsIHByZXZEYXRhKTtcbiAgICBpZiAoZGF0YSA9PT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm4gdm9pZCAwO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKS5zZXREYXRhKGRhdGEsIHsgLi4ub3B0aW9ucywgbWFudWFsOiB0cnVlIH0pO1xuICB9XG4gIHNldFF1ZXJpZXNEYXRhKGZpbHRlcnMsIHVwZGF0ZXIsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbm90aWZ5TWFuYWdlci5iYXRjaChcbiAgICAgICgpID0+IHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbChmaWx0ZXJzKS5tYXAoKHsgcXVlcnlLZXkgfSkgPT4gW1xuICAgICAgICBxdWVyeUtleSxcbiAgICAgICAgdGhpcy5zZXRRdWVyeURhdGEocXVlcnlLZXksIHVwZGF0ZXIsIG9wdGlvbnMpXG4gICAgICBdKVxuICAgICk7XG4gIH1cbiAgZ2V0UXVlcnlTdGF0ZShxdWVyeUtleSkge1xuICAgIGNvbnN0IG9wdGlvbnMgPSB0aGlzLmRlZmF1bHRRdWVyeU9wdGlvbnMoeyBxdWVyeUtleSB9KTtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZS5nZXQoXG4gICAgICBvcHRpb25zLnF1ZXJ5SGFzaFxuICAgICk/LnN0YXRlO1xuICB9XG4gIHJlbW92ZVF1ZXJpZXMoZmlsdGVycykge1xuICAgIGNvbnN0IHF1ZXJ5Q2FjaGUgPSB0aGlzLiNxdWVyeUNhY2hlO1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHF1ZXJ5Q2FjaGUucmVtb3ZlKHF1ZXJ5KTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9XG4gIHJlc2V0UXVlcmllcyhmaWx0ZXJzLCBvcHRpb25zKSB7XG4gICAgY29uc3QgcXVlcnlDYWNoZSA9IHRoaXMuI3F1ZXJ5Q2FjaGU7XG4gICAgcmV0dXJuIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHF1ZXJ5LnJlc2V0KCk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiB0aGlzLnJlZmV0Y2hRdWVyaWVzKFxuICAgICAgICB7XG4gICAgICAgICAgdHlwZTogXCJhY3RpdmVcIixcbiAgICAgICAgICAuLi5maWx0ZXJzXG4gICAgICAgIH0sXG4gICAgICAgIG9wdGlvbnNcbiAgICAgICk7XG4gICAgfSk7XG4gIH1cbiAgY2FuY2VsUXVlcmllcyhmaWx0ZXJzLCBjYW5jZWxPcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRDYW5jZWxPcHRpb25zID0geyByZXZlcnQ6IHRydWUsIC4uLmNhbmNlbE9wdGlvbnMgfTtcbiAgICBjb25zdCBwcm9taXNlcyA9IG5vdGlmeU1hbmFnZXIuYmF0Y2goXG4gICAgICAoKSA9PiB0aGlzLiNxdWVyeUNhY2hlLmZpbmRBbGwoZmlsdGVycykubWFwKChxdWVyeSkgPT4gcXVlcnkuY2FuY2VsKGRlZmF1bHRlZENhbmNlbE9wdGlvbnMpKVxuICAgICk7XG4gICAgcmV0dXJuIFByb21pc2UuYWxsKHByb21pc2VzKS50aGVuKG5vb3ApLmNhdGNoKG5vb3ApO1xuICB9XG4gIGludmFsaWRhdGVRdWVyaWVzKGZpbHRlcnMsIG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiBub3RpZnlNYW5hZ2VyLmJhdGNoKCgpID0+IHtcbiAgICAgIHRoaXMuI3F1ZXJ5Q2FjaGUuZmluZEFsbChmaWx0ZXJzKS5mb3JFYWNoKChxdWVyeSkgPT4ge1xuICAgICAgICBxdWVyeS5pbnZhbGlkYXRlKCk7XG4gICAgICB9KTtcbiAgICAgIGlmIChmaWx0ZXJzPy5yZWZldGNoVHlwZSA9PT0gXCJub25lXCIpIHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRoaXMucmVmZXRjaFF1ZXJpZXMoXG4gICAgICAgIHtcbiAgICAgICAgICAuLi5maWx0ZXJzLFxuICAgICAgICAgIHR5cGU6IGZpbHRlcnM/LnJlZmV0Y2hUeXBlID8/IGZpbHRlcnM/LnR5cGUgPz8gXCJhY3RpdmVcIlxuICAgICAgICB9LFxuICAgICAgICBvcHRpb25zXG4gICAgICApO1xuICAgIH0pO1xuICB9XG4gIHJlZmV0Y2hRdWVyaWVzKGZpbHRlcnMsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IGZldGNoT3B0aW9ucyA9IHtcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBjYW5jZWxSZWZldGNoOiBvcHRpb25zLmNhbmNlbFJlZmV0Y2ggPz8gdHJ1ZVxuICAgIH07XG4gICAgY29uc3QgcHJvbWlzZXMgPSBub3RpZnlNYW5hZ2VyLmJhdGNoKFxuICAgICAgKCkgPT4gdGhpcy4jcXVlcnlDYWNoZS5maW5kQWxsKGZpbHRlcnMpLmZpbHRlcigocXVlcnkpID0+ICFxdWVyeS5pc0Rpc2FibGVkKCkgJiYgIXF1ZXJ5LmlzU3RhdGljKCkpLm1hcCgocXVlcnkpID0+IHtcbiAgICAgICAgbGV0IHByb21pc2UgPSBxdWVyeS5mZXRjaCh2b2lkIDAsIGZldGNoT3B0aW9ucyk7XG4gICAgICAgIGlmICghZmV0Y2hPcHRpb25zLnRocm93T25FcnJvcikge1xuICAgICAgICAgIHByb21pc2UgPSBwcm9taXNlLmNhdGNoKG5vb3ApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBxdWVyeS5zdGF0ZS5mZXRjaFN0YXR1cyA9PT0gXCJwYXVzZWRcIiA/IFByb21pc2UucmVzb2x2ZSgpIDogcHJvbWlzZTtcbiAgICAgIH0pXG4gICAgKTtcbiAgICByZXR1cm4gUHJvbWlzZS5hbGwocHJvbWlzZXMpLnRoZW4obm9vcCk7XG4gIH1cbiAgZmV0Y2hRdWVyeShvcHRpb25zKSB7XG4gICAgY29uc3QgZGVmYXVsdGVkT3B0aW9ucyA9IHRoaXMuZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKTtcbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5yZXRyeSA9PT0gdm9pZCAwKSB7XG4gICAgICBkZWZhdWx0ZWRPcHRpb25zLnJldHJ5ID0gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IHF1ZXJ5ID0gdGhpcy4jcXVlcnlDYWNoZS5idWlsZCh0aGlzLCBkZWZhdWx0ZWRPcHRpb25zKTtcbiAgICByZXR1cm4gcXVlcnkuaXNTdGFsZUJ5VGltZShcbiAgICAgIHJlc29sdmVTdGFsZVRpbWUoZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUsIHF1ZXJ5KVxuICAgICkgPyBxdWVyeS5mZXRjaChkZWZhdWx0ZWRPcHRpb25zKSA6IFByb21pc2UucmVzb2x2ZShxdWVyeS5zdGF0ZS5kYXRhKTtcbiAgfVxuICBwcmVmZXRjaFF1ZXJ5KG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5mZXRjaFF1ZXJ5KG9wdGlvbnMpLnRoZW4obm9vcCkuY2F0Y2gobm9vcCk7XG4gIH1cbiAgZmV0Y2hJbmZpbml0ZVF1ZXJ5KG9wdGlvbnMpIHtcbiAgICBvcHRpb25zLmJlaGF2aW9yID0gaW5maW5pdGVRdWVyeUJlaGF2aW9yKG9wdGlvbnMucGFnZXMpO1xuICAgIHJldHVybiB0aGlzLmZldGNoUXVlcnkob3B0aW9ucyk7XG4gIH1cbiAgcHJlZmV0Y2hJbmZpbml0ZVF1ZXJ5KG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5mZXRjaEluZmluaXRlUXVlcnkob3B0aW9ucykudGhlbihub29wKS5jYXRjaChub29wKTtcbiAgfVxuICBlbnN1cmVJbmZpbml0ZVF1ZXJ5RGF0YShvcHRpb25zKSB7XG4gICAgb3B0aW9ucy5iZWhhdmlvciA9IGluZmluaXRlUXVlcnlCZWhhdmlvcihvcHRpb25zLnBhZ2VzKTtcbiAgICByZXR1cm4gdGhpcy5lbnN1cmVRdWVyeURhdGEob3B0aW9ucyk7XG4gIH1cbiAgcmVzdW1lUGF1c2VkTXV0YXRpb25zKCkge1xuICAgIGlmIChvbmxpbmVNYW5hZ2VyLmlzT25saW5lKCkpIHtcbiAgICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlLnJlc3VtZVBhdXNlZE11dGF0aW9ucygpO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gIH1cbiAgZ2V0UXVlcnlDYWNoZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcXVlcnlDYWNoZTtcbiAgfVxuICBnZXRNdXRhdGlvbkNhY2hlKCkge1xuICAgIHJldHVybiB0aGlzLiNtdXRhdGlvbkNhY2hlO1xuICB9XG4gIGdldERlZmF1bHRPcHRpb25zKCkge1xuICAgIHJldHVybiB0aGlzLiNkZWZhdWx0T3B0aW9ucztcbiAgfVxuICBzZXREZWZhdWx0T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdGhpcy4jZGVmYXVsdE9wdGlvbnMgPSBvcHRpb25zO1xuICB9XG4gIHNldFF1ZXJ5RGVmYXVsdHMocXVlcnlLZXksIG9wdGlvbnMpIHtcbiAgICB0aGlzLiNxdWVyeURlZmF1bHRzLnNldChoYXNoS2V5KHF1ZXJ5S2V5KSwge1xuICAgICAgcXVlcnlLZXksXG4gICAgICBkZWZhdWx0T3B0aW9uczogb3B0aW9uc1xuICAgIH0pO1xuICB9XG4gIGdldFF1ZXJ5RGVmYXVsdHMocXVlcnlLZXkpIHtcbiAgICBjb25zdCBkZWZhdWx0cyA9IFsuLi50aGlzLiNxdWVyeURlZmF1bHRzLnZhbHVlcygpXTtcbiAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICBkZWZhdWx0cy5mb3JFYWNoKChxdWVyeURlZmF1bHQpID0+IHtcbiAgICAgIGlmIChwYXJ0aWFsTWF0Y2hLZXkocXVlcnlLZXksIHF1ZXJ5RGVmYXVsdC5xdWVyeUtleSkpIHtcbiAgICAgICAgT2JqZWN0LmFzc2lnbihyZXN1bHQsIHF1ZXJ5RGVmYXVsdC5kZWZhdWx0T3B0aW9ucyk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBzZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5LCBvcHRpb25zKSB7XG4gICAgdGhpcy4jbXV0YXRpb25EZWZhdWx0cy5zZXQoaGFzaEtleShtdXRhdGlvbktleSksIHtcbiAgICAgIG11dGF0aW9uS2V5LFxuICAgICAgZGVmYXVsdE9wdGlvbnM6IG9wdGlvbnNcbiAgICB9KTtcbiAgfVxuICBnZXRNdXRhdGlvbkRlZmF1bHRzKG11dGF0aW9uS2V5KSB7XG4gICAgY29uc3QgZGVmYXVsdHMgPSBbLi4udGhpcy4jbXV0YXRpb25EZWZhdWx0cy52YWx1ZXMoKV07XG4gICAgY29uc3QgcmVzdWx0ID0ge307XG4gICAgZGVmYXVsdHMuZm9yRWFjaCgocXVlcnlEZWZhdWx0KSA9PiB7XG4gICAgICBpZiAocGFydGlhbE1hdGNoS2V5KG11dGF0aW9uS2V5LCBxdWVyeURlZmF1bHQubXV0YXRpb25LZXkpKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24ocmVzdWx0LCBxdWVyeURlZmF1bHQuZGVmYXVsdE9wdGlvbnMpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbiAgZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgaWYgKG9wdGlvbnMuX2RlZmF1bHRlZCkge1xuICAgICAgcmV0dXJuIG9wdGlvbnM7XG4gICAgfVxuICAgIGNvbnN0IGRlZmF1bHRlZE9wdGlvbnMgPSB7XG4gICAgICAuLi50aGlzLiNkZWZhdWx0T3B0aW9ucy5xdWVyaWVzLFxuICAgICAgLi4udGhpcy5nZXRRdWVyeURlZmF1bHRzKG9wdGlvbnMucXVlcnlLZXkpLFxuICAgICAgLi4ub3B0aW9ucyxcbiAgICAgIF9kZWZhdWx0ZWQ6IHRydWVcbiAgICB9O1xuICAgIGlmICghZGVmYXVsdGVkT3B0aW9ucy5xdWVyeUhhc2gpIHtcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNoID0gaGFzaFF1ZXJ5S2V5QnlPcHRpb25zKFxuICAgICAgICBkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5S2V5LFxuICAgICAgICBkZWZhdWx0ZWRPcHRpb25zXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5yZWZldGNoT25SZWNvbm5lY3QgPT09IHZvaWQgMCkge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5yZWZldGNoT25SZWNvbm5lY3QgPSBkZWZhdWx0ZWRPcHRpb25zLm5ldHdvcmtNb2RlICE9PSBcImFsd2F5c1wiO1xuICAgIH1cbiAgICBpZiAoZGVmYXVsdGVkT3B0aW9ucy50aHJvd09uRXJyb3IgPT09IHZvaWQgMCkge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy50aHJvd09uRXJyb3IgPSAhIWRlZmF1bHRlZE9wdGlvbnMuc3VzcGVuc2U7XG4gICAgfVxuICAgIGlmICghZGVmYXVsdGVkT3B0aW9ucy5uZXR3b3JrTW9kZSAmJiBkZWZhdWx0ZWRPcHRpb25zLnBlcnNpc3Rlcikge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5uZXR3b3JrTW9kZSA9IFwib2ZmbGluZUZpcnN0XCI7XG4gICAgfVxuICAgIGlmIChkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5Rm4gPT09IHNraXBUb2tlbikge1xuICAgICAgZGVmYXVsdGVkT3B0aW9ucy5lbmFibGVkID0gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiBkZWZhdWx0ZWRPcHRpb25zO1xuICB9XG4gIGRlZmF1bHRNdXRhdGlvbk9wdGlvbnMob3B0aW9ucykge1xuICAgIGlmIChvcHRpb25zPy5fZGVmYXVsdGVkKSB7XG4gICAgICByZXR1cm4gb3B0aW9ucztcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnRoaXMuI2RlZmF1bHRPcHRpb25zLm11dGF0aW9ucyxcbiAgICAgIC4uLm9wdGlvbnM/Lm11dGF0aW9uS2V5ICYmIHRoaXMuZ2V0TXV0YXRpb25EZWZhdWx0cyhvcHRpb25zLm11dGF0aW9uS2V5KSxcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBfZGVmYXVsdGVkOiB0cnVlXG4gICAgfTtcbiAgfVxuICBjbGVhcigpIHtcbiAgICB0aGlzLiNxdWVyeUNhY2hlLmNsZWFyKCk7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZS5jbGVhcigpO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgUXVlcnlDbGllbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeUNsaWVudC5qcy5tYXAiXSwibmFtZXMiOlsiZnVuY3Rpb25hbFVwZGF0ZSIsImhhc2hLZXkiLCJoYXNoUXVlcnlLZXlCeU9wdGlvbnMiLCJub29wIiwicGFydGlhbE1hdGNoS2V5IiwicmVzb2x2ZVN0YWxlVGltZSIsInNraXBUb2tlbiIsIlF1ZXJ5Q2FjaGUiLCJNdXRhdGlvbkNhY2hlIiwiZm9jdXNNYW5hZ2VyIiwib25saW5lTWFuYWdlciIsIm5vdGlmeU1hbmFnZXIiLCJpbmZpbml0ZVF1ZXJ5QmVoYXZpb3IiLCJRdWVyeUNsaWVudCIsInF1ZXJ5Q2FjaGUiLCJtdXRhdGlvbkNhY2hlIiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyeURlZmF1bHRzIiwibXV0YXRpb25EZWZhdWx0cyIsIm1vdW50Q291bnQiLCJ1bnN1YnNjcmliZUZvY3VzIiwidW5zdWJzY3JpYmVPbmxpbmUiLCJjb25zdHJ1Y3RvciIsImNvbmZpZyIsIk1hcCIsIm1vdW50Iiwic3Vic2NyaWJlIiwiZm9jdXNlZCIsInJlc3VtZVBhdXNlZE11dGF0aW9ucyIsIm9uRm9jdXMiLCJvbmxpbmUiLCJvbk9ubGluZSIsInVubW91bnQiLCJpc0ZldGNoaW5nIiwiZmlsdGVycyIsImZpbmRBbGwiLCJmZXRjaFN0YXR1cyIsImxlbmd0aCIsImlzTXV0YXRpbmciLCJzdGF0dXMiLCJnZXRRdWVyeURhdGEiLCJxdWVyeUtleSIsIm9wdGlvbnMiLCJkZWZhdWx0UXVlcnlPcHRpb25zIiwiZ2V0IiwicXVlcnlIYXNoIiwic3RhdGUiLCJkYXRhIiwiZW5zdXJlUXVlcnlEYXRhIiwiZGVmYXVsdGVkT3B0aW9ucyIsInF1ZXJ5IiwiYnVpbGQiLCJjYWNoZWREYXRhIiwiZmV0Y2hRdWVyeSIsInJldmFsaWRhdGVJZlN0YWxlIiwiaXNTdGFsZUJ5VGltZSIsInN0YWxlVGltZSIsInByZWZldGNoUXVlcnkiLCJQcm9taXNlIiwicmVzb2x2ZSIsImdldFF1ZXJpZXNEYXRhIiwibWFwIiwic2V0UXVlcnlEYXRhIiwidXBkYXRlciIsInByZXZEYXRhIiwic2V0RGF0YSIsIm1hbnVhbCIsInNldFF1ZXJpZXNEYXRhIiwiYmF0Y2giLCJnZXRRdWVyeVN0YXRlIiwicmVtb3ZlUXVlcmllcyIsImZvckVhY2giLCJyZW1vdmUiLCJyZXNldFF1ZXJpZXMiLCJyZXNldCIsInJlZmV0Y2hRdWVyaWVzIiwidHlwZSIsImNhbmNlbFF1ZXJpZXMiLCJjYW5jZWxPcHRpb25zIiwiZGVmYXVsdGVkQ2FuY2VsT3B0aW9ucyIsInJldmVydCIsInByb21pc2VzIiwiY2FuY2VsIiwiYWxsIiwidGhlbiIsImNhdGNoIiwiaW52YWxpZGF0ZVF1ZXJpZXMiLCJpbnZhbGlkYXRlIiwicmVmZXRjaFR5cGUiLCJmZXRjaE9wdGlvbnMiLCJjYW5jZWxSZWZldGNoIiwiZmlsdGVyIiwiaXNEaXNhYmxlZCIsImlzU3RhdGljIiwicHJvbWlzZSIsImZldGNoIiwidGhyb3dPbkVycm9yIiwicmV0cnkiLCJmZXRjaEluZmluaXRlUXVlcnkiLCJiZWhhdmlvciIsInBhZ2VzIiwicHJlZmV0Y2hJbmZpbml0ZVF1ZXJ5IiwiZW5zdXJlSW5maW5pdGVRdWVyeURhdGEiLCJpc09ubGluZSIsImdldFF1ZXJ5Q2FjaGUiLCJnZXRNdXRhdGlvbkNhY2hlIiwiZ2V0RGVmYXVsdE9wdGlvbnMiLCJzZXREZWZhdWx0T3B0aW9ucyIsInNldFF1ZXJ5RGVmYXVsdHMiLCJzZXQiLCJnZXRRdWVyeURlZmF1bHRzIiwiZGVmYXVsdHMiLCJ2YWx1ZXMiLCJyZXN1bHQiLCJxdWVyeURlZmF1bHQiLCJPYmplY3QiLCJhc3NpZ24iLCJzZXRNdXRhdGlvbkRlZmF1bHRzIiwibXV0YXRpb25LZXkiLCJnZXRNdXRhdGlvbkRlZmF1bHRzIiwiX2RlZmF1bHRlZCIsInF1ZXJpZXMiLCJyZWZldGNoT25SZWNvbm5lY3QiLCJuZXR3b3JrTW9kZSIsInN1c3BlbnNlIiwicGVyc2lzdGVyIiwicXVlcnlGbiIsImVuYWJsZWQiLCJkZWZhdWx0TXV0YXRpb25PcHRpb25zIiwibXV0YXRpb25zIiwiY2xlYXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryObserver.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/queryObserver.ts\n\n\n\n\n\n\nvar QueryObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(client, options){\n        super();\n        this.#currentQuery = void 0;\n        this.#currentQueryInitialState = void 0;\n        this.#currentResult = void 0;\n        this.#trackedProps = /* @__PURE__ */ new Set();\n        this.options = options;\n        this.#client = client;\n        this.#selectError = null;\n        this.#currentThenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n        if (!this.options.experimental_prefetchInRender) {\n            this.#currentThenable.reject(new Error(\"experimental_prefetchInRender feature flag is not enabled\"));\n        }\n        this.bindMethods();\n        this.setOptions(options);\n    }\n    #client;\n    #currentQuery;\n    #currentQueryInitialState;\n    #currentResult;\n    #currentResultState;\n    #currentResultOptions;\n    #currentThenable;\n    #selectError;\n    #selectFn;\n    #selectResult;\n    // This property keeps track of the last query with defined data.\n    // It will be used to pass the previous data and query to the placeholder function between renders.\n    #lastQueryWithDefinedData;\n    #staleTimeoutId;\n    #refetchIntervalId;\n    #currentRefetchInterval;\n    #trackedProps;\n    bindMethods() {\n        this.refetch = this.refetch.bind(this);\n    }\n    onSubscribe() {\n        if (this.listeners.size === 1) {\n            this.#currentQuery.addObserver(this);\n            if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n                this.#executeFetch();\n            } else {\n                this.updateResult();\n            }\n            this.#updateTimers();\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.destroy();\n        }\n    }\n    shouldFetchOnReconnect() {\n        return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnReconnect);\n    }\n    shouldFetchOnWindowFocus() {\n        return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnWindowFocus);\n    }\n    destroy() {\n        this.listeners = /* @__PURE__ */ new Set();\n        this.#clearStaleTimeout();\n        this.#clearRefetchInterval();\n        this.#currentQuery.removeObserver(this);\n    }\n    setOptions(options) {\n        const prevOptions = this.options;\n        const prevQuery = this.#currentQuery;\n        this.options = this.#client.defaultQueryOptions(options);\n        if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n            throw new Error(\"Expected enabled to be a boolean or a callback that returns a boolean\");\n        }\n        this.#updateQuery();\n        this.#currentQuery.setOptions(this.options);\n        if (prevOptions._defaulted && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.options, prevOptions)) {\n            this.#client.getQueryCache().notify({\n                type: \"observerOptionsUpdated\",\n                query: this.#currentQuery,\n                observer: this\n            });\n        }\n        const mounted = this.hasListeners();\n        if (mounted && shouldFetchOptionally(this.#currentQuery, prevQuery, this.options, prevOptions)) {\n            this.#executeFetch();\n        }\n        this.updateResult();\n        if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(prevOptions.staleTime, this.#currentQuery))) {\n            this.#updateStaleTimeout();\n        }\n        const nextRefetchInterval = this.#computeRefetchInterval();\n        if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n            this.#updateRefetchInterval(nextRefetchInterval);\n        }\n    }\n    getOptimisticResult(options) {\n        const query = this.#client.getQueryCache().build(this.#client, options);\n        const result = this.createResult(query, options);\n        if (shouldAssignObserverCurrentProperties(this, result)) {\n            this.#currentResult = result;\n            this.#currentResultOptions = this.options;\n            this.#currentResultState = this.#currentQuery.state;\n        }\n        return result;\n    }\n    getCurrentResult() {\n        return this.#currentResult;\n    }\n    trackResult(result, onPropTracked) {\n        return new Proxy(result, {\n            get: (target, key)=>{\n                this.trackProp(key);\n                onPropTracked?.(key);\n                return Reflect.get(target, key);\n            }\n        });\n    }\n    trackProp(key) {\n        this.#trackedProps.add(key);\n    }\n    getCurrentQuery() {\n        return this.#currentQuery;\n    }\n    refetch({ ...options } = {}) {\n        return this.fetch({\n            ...options\n        });\n    }\n    fetchOptimistic(options) {\n        const defaultedOptions = this.#client.defaultQueryOptions(options);\n        const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n        return query.fetch().then(()=>this.createResult(query, defaultedOptions));\n    }\n    fetch(fetchOptions) {\n        return this.#executeFetch({\n            ...fetchOptions,\n            cancelRefetch: fetchOptions.cancelRefetch ?? true\n        }).then(()=>{\n            this.updateResult();\n            return this.#currentResult;\n        });\n    }\n    #executeFetch(fetchOptions) {\n        this.#updateQuery();\n        let promise = this.#currentQuery.fetch(this.options, fetchOptions);\n        if (!fetchOptions?.throwOnError) {\n            promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_2__.noop);\n        }\n        return promise;\n    }\n    #updateStaleTimeout() {\n        this.#clearStaleTimeout();\n        const staleTime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery);\n        if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || this.#currentResult.isStale || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(staleTime)) {\n            return;\n        }\n        const time = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.#currentResult.dataUpdatedAt, staleTime);\n        const timeout = time + 1;\n        this.#staleTimeoutId = setTimeout(()=>{\n            if (!this.#currentResult.isStale) {\n                this.updateResult();\n            }\n        }, timeout);\n    }\n    #computeRefetchInterval() {\n        return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n    }\n    #updateRefetchInterval(nextInterval) {\n        this.#clearRefetchInterval();\n        this.#currentRefetchInterval = nextInterval;\n        if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) === false || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n            return;\n        }\n        this.#refetchIntervalId = setInterval(()=>{\n            if (this.options.refetchIntervalInBackground || _focusManager_js__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n                this.#executeFetch();\n            }\n        }, this.#currentRefetchInterval);\n    }\n    #updateTimers() {\n        this.#updateStaleTimeout();\n        this.#updateRefetchInterval(this.#computeRefetchInterval());\n    }\n    #clearStaleTimeout() {\n        if (this.#staleTimeoutId) {\n            clearTimeout(this.#staleTimeoutId);\n            this.#staleTimeoutId = void 0;\n        }\n    }\n    #clearRefetchInterval() {\n        if (this.#refetchIntervalId) {\n            clearInterval(this.#refetchIntervalId);\n            this.#refetchIntervalId = void 0;\n        }\n    }\n    createResult(query, options) {\n        const prevQuery = this.#currentQuery;\n        const prevOptions = this.options;\n        const prevResult = this.#currentResult;\n        const prevResultState = this.#currentResultState;\n        const prevResultOptions = this.#currentResultOptions;\n        const queryChange = query !== prevQuery;\n        const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n        const { state } = query;\n        let newState = {\n            ...state\n        };\n        let isPlaceholderData = false;\n        let data;\n        if (options._optimisticResults) {\n            const mounted = this.hasListeners();\n            const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n            const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n            if (fetchOnMount || fetchOptionally) {\n                newState = {\n                    ...newState,\n                    ...(0,_query_js__WEBPACK_IMPORTED_MODULE_4__.fetchState)(state.data, query.options)\n                };\n            }\n            if (options._optimisticResults === \"isRestoring\") {\n                newState.fetchStatus = \"idle\";\n            }\n        }\n        let { error, errorUpdatedAt, status } = newState;\n        data = newState.data;\n        let skipSelect = false;\n        if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n            let placeholderData;\n            if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n                placeholderData = prevResult.data;\n                skipSelect = true;\n            } else {\n                placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(this.#lastQueryWithDefinedData?.state.data, this.#lastQueryWithDefinedData) : options.placeholderData;\n            }\n            if (placeholderData !== void 0) {\n                status = \"success\";\n                data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, placeholderData, options);\n                isPlaceholderData = true;\n            }\n        }\n        if (options.select && data !== void 0 && !skipSelect) {\n            if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n                data = this.#selectResult;\n            } else {\n                try {\n                    this.#selectFn = options.select;\n                    data = options.select(data);\n                    data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, data, options);\n                    this.#selectResult = data;\n                    this.#selectError = null;\n                } catch (selectError) {\n                    this.#selectError = selectError;\n                }\n            }\n        }\n        if (this.#selectError) {\n            error = this.#selectError;\n            data = this.#selectResult;\n            errorUpdatedAt = Date.now();\n            status = \"error\";\n        }\n        const isFetching = newState.fetchStatus === \"fetching\";\n        const isPending = status === \"pending\";\n        const isError = status === \"error\";\n        const isLoading = isPending && isFetching;\n        const hasData = data !== void 0;\n        const result = {\n            status,\n            fetchStatus: newState.fetchStatus,\n            isPending,\n            isSuccess: status === \"success\",\n            isError,\n            isInitialLoading: isLoading,\n            isLoading,\n            data,\n            dataUpdatedAt: newState.dataUpdatedAt,\n            error,\n            errorUpdatedAt,\n            failureCount: newState.fetchFailureCount,\n            failureReason: newState.fetchFailureReason,\n            errorUpdateCount: newState.errorUpdateCount,\n            isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n            isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n            isFetching,\n            isRefetching: isFetching && !isPending,\n            isLoadingError: isError && !hasData,\n            isPaused: newState.fetchStatus === \"paused\",\n            isPlaceholderData,\n            isRefetchError: isError && hasData,\n            isStale: isStale(query, options),\n            refetch: this.refetch,\n            promise: this.#currentThenable\n        };\n        const nextResult = result;\n        if (this.options.experimental_prefetchInRender) {\n            const finalizeThenableIfPossible = (thenable)=>{\n                if (nextResult.status === \"error\") {\n                    thenable.reject(nextResult.error);\n                } else if (nextResult.data !== void 0) {\n                    thenable.resolve(nextResult.data);\n                }\n            };\n            const recreateThenable = ()=>{\n                const pending = this.#currentThenable = nextResult.promise = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n                finalizeThenableIfPossible(pending);\n            };\n            const prevThenable = this.#currentThenable;\n            switch(prevThenable.status){\n                case \"pending\":\n                    if (query.queryHash === prevQuery.queryHash) {\n                        finalizeThenableIfPossible(prevThenable);\n                    }\n                    break;\n                case \"fulfilled\":\n                    if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n                        recreateThenable();\n                    }\n                    break;\n                case \"rejected\":\n                    if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n                        recreateThenable();\n                    }\n                    break;\n            }\n        }\n        return nextResult;\n    }\n    updateResult() {\n        const prevResult = this.#currentResult;\n        const nextResult = this.createResult(this.#currentQuery, this.options);\n        this.#currentResultState = this.#currentQuery.state;\n        this.#currentResultOptions = this.options;\n        if (this.#currentResultState.data !== void 0) {\n            this.#lastQueryWithDefinedData = this.#currentQuery;\n        }\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(nextResult, prevResult)) {\n            return;\n        }\n        this.#currentResult = nextResult;\n        const shouldNotifyListeners = ()=>{\n            if (!prevResult) {\n                return true;\n            }\n            const { notifyOnChangeProps } = this.options;\n            const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n            if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n                return true;\n            }\n            const includedProps = new Set(notifyOnChangePropsValue ?? this.#trackedProps);\n            if (this.options.throwOnError) {\n                includedProps.add(\"error\");\n            }\n            return Object.keys(this.#currentResult).some((key)=>{\n                const typedKey = key;\n                const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n                return changed && includedProps.has(typedKey);\n            });\n        };\n        this.#notify({\n            listeners: shouldNotifyListeners()\n        });\n    }\n    #updateQuery() {\n        const query = this.#client.getQueryCache().build(this.#client, this.options);\n        if (query === this.#currentQuery) {\n            return;\n        }\n        const prevQuery = this.#currentQuery;\n        this.#currentQuery = query;\n        this.#currentQueryInitialState = query.state;\n        if (this.hasListeners()) {\n            prevQuery?.removeObserver(this);\n            query.addObserver(this);\n        }\n    }\n    onQueryUpdate() {\n        this.updateResult();\n        if (this.hasListeners()) {\n            this.#updateTimers();\n        }\n    }\n    #notify(notifyOptions) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            if (notifyOptions.listeners) {\n                this.listeners.forEach((listener)=>{\n                    listener(this.#currentResult);\n                });\n            }\n            this.#client.getQueryCache().notify({\n                query: this.#currentQuery,\n                type: \"observerResultsUpdated\"\n            });\n        });\n    }\n};\nfunction shouldLoadOnMount(query, options) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n    return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query) !== \"static\") {\n        const value = typeof field === \"function\" ? field(query) : field;\n        return value === \"always\" || value !== false && isStale(query, options);\n    }\n    return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n    return (query !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n        return true;\n    }\n    return false;\n}\n //# sourceMappingURL=queryObserver.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n    #gcTimeout;\n    destroy() {\n        this.clearGcTimeout();\n    }\n    scheduleGc() {\n        this.clearGcTimeout();\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n            this.#gcTimeout = setTimeout(()=>{\n                this.optionalRemove();\n            }, this.gcTime);\n        }\n    }\n    updateGcTime(newGcTime) {\n        this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3));\n    }\n    clearGcTimeout() {\n        if (this.#gcTimeout) {\n            clearTimeout(this.#gcTimeout);\n            this.#gcTimeout = void 0;\n        }\n    }\n};\n //# sourceMappingURL=removable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n    return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n    return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n    constructor(options){\n        super(\"CancelledError\");\n        this.revert = options?.revert;\n        this.silent = options?.silent;\n    }\n};\nfunction isCancelledError(value) {\n    return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n    let isRetryCancelled = false;\n    let failureCount = 0;\n    let isResolved = false;\n    let continueFn;\n    const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    const cancel = (cancelOptions)=>{\n        if (!isResolved) {\n            reject(new CancelledError(cancelOptions));\n            config.abort?.();\n        }\n    };\n    const cancelRetry = ()=>{\n        isRetryCancelled = true;\n    };\n    const continueRetry = ()=>{\n        isRetryCancelled = false;\n    };\n    const canContinue = ()=>_focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n    const canStart = ()=>canFetch(config.networkMode) && config.canRun();\n    const resolve = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onSuccess?.(value);\n            continueFn?.();\n            thenable.resolve(value);\n        }\n    };\n    const reject = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onError?.(value);\n            continueFn?.();\n            thenable.reject(value);\n        }\n    };\n    const pause = ()=>{\n        return new Promise((continueResolve)=>{\n            continueFn = (value)=>{\n                if (isResolved || canContinue()) {\n                    continueResolve(value);\n                }\n            };\n            config.onPause?.();\n        }).then(()=>{\n            continueFn = void 0;\n            if (!isResolved) {\n                config.onContinue?.();\n            }\n        });\n    };\n    const run = ()=>{\n        if (isResolved) {\n            return;\n        }\n        let promiseOrValue;\n        const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n        try {\n            promiseOrValue = initialPromise ?? config.fn();\n        } catch (error) {\n            promiseOrValue = Promise.reject(error);\n        }\n        Promise.resolve(promiseOrValue).then(resolve).catch((error)=>{\n            if (isResolved) {\n                return;\n            }\n            const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n            const retryDelay = config.retryDelay ?? defaultRetryDelay;\n            const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n            const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n            if (isRetryCancelled || !shouldRetry) {\n                reject(error);\n                return;\n            }\n            failureCount++;\n            config.onFail?.(failureCount, error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(()=>{\n                return canContinue() ? void 0 : pause();\n            }).then(()=>{\n                if (isRetryCancelled) {\n                    reject(error);\n                } else {\n                    run();\n                }\n            });\n        });\n    };\n    return {\n        promise: thenable,\n        cancel,\n        continue: ()=>{\n            continueFn?.();\n            return thenable;\n        },\n        cancelRetry,\n        continueRetry,\n        canStart,\n        start: ()=>{\n            if (canStart()) {\n                run();\n            } else {\n                pause().then(run);\n            }\n            return thenable;\n        }\n    };\n}\n //# sourceMappingURL=retryer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n    constructor(){\n        this.listeners = /* @__PURE__ */ new Set();\n        this.subscribe = this.subscribe.bind(this);\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        this.onSubscribe();\n        return ()=>{\n            this.listeners.delete(listener);\n            this.onUnsubscribe();\n        };\n    }\n    hasListeners() {\n        return this.listeners.size > 0;\n    }\n    onSubscribe() {}\n    onUnsubscribe() {}\n};\n //# sourceMappingURL=subscribable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsc0JBQXNCO0FBQ3RCLElBQUlBLGVBQWU7SUFDakJDLGFBQWM7UUFDWixJQUFJLENBQUNDLFNBQVMsR0FBRyxhQUFhLEdBQUcsSUFBSUM7UUFDckMsSUFBSSxDQUFDQyxTQUFTLEdBQUcsSUFBSSxDQUFDQSxTQUFTLENBQUNDLElBQUksQ0FBQyxJQUFJO0lBQzNDO0lBQ0FELFVBQVVFLFFBQVEsRUFBRTtRQUNsQixJQUFJLENBQUNKLFNBQVMsQ0FBQ0ssR0FBRyxDQUFDRDtRQUNuQixJQUFJLENBQUNFLFdBQVc7UUFDaEIsT0FBTztZQUNMLElBQUksQ0FBQ04sU0FBUyxDQUFDTyxNQUFNLENBQUNIO1lBQ3RCLElBQUksQ0FBQ0ksYUFBYTtRQUNwQjtJQUNGO0lBQ0FDLGVBQWU7UUFDYixPQUFPLElBQUksQ0FBQ1QsU0FBUyxDQUFDVSxJQUFJLEdBQUc7SUFDL0I7SUFDQUosY0FBYyxDQUNkO0lBQ0FFLGdCQUFnQixDQUNoQjtBQUNGO0FBR0UsQ0FDRix3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZWFzZS1hZG1pbi1wYW5lbC8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vc3Vic2NyaWJhYmxlLmpzP2JlYmMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3N1YnNjcmliYWJsZS50c1xudmFyIFN1YnNjcmliYWJsZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gdGhpcy5zdWJzY3JpYmUuYmluZCh0aGlzKTtcbiAgfVxuICBzdWJzY3JpYmUobGlzdGVuZXIpIHtcbiAgICB0aGlzLmxpc3RlbmVycy5hZGQobGlzdGVuZXIpO1xuICAgIHRoaXMub25TdWJzY3JpYmUoKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyKTtcbiAgICAgIHRoaXMub25VbnN1YnNjcmliZSgpO1xuICAgIH07XG4gIH1cbiAgaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5zaXplID4gMDtcbiAgfVxuICBvblN1YnNjcmliZSgpIHtcbiAgfVxuICBvblVuc3Vic2NyaWJlKCkge1xuICB9XG59O1xuZXhwb3J0IHtcbiAgU3Vic2NyaWJhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Vic2NyaWJhYmxlLmpzLm1hcCJdLCJuYW1lcyI6WyJTdWJzY3JpYmFibGUiLCJjb25zdHJ1Y3RvciIsImxpc3RlbmVycyIsIlNldCIsInN1YnNjcmliZSIsImJpbmQiLCJsaXN0ZW5lciIsImFkZCIsIm9uU3Vic2NyaWJlIiwiZGVsZXRlIiwib25VbnN1YnNjcmliZSIsImhhc0xpc3RlbmVycyIsInNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n    let resolve;\n    let reject;\n    const thenable = new Promise((_resolve, _reject)=>{\n        resolve = _resolve;\n        reject = _reject;\n    });\n    thenable.status = \"pending\";\n    thenable.catch(()=>{});\n    function finalize(data) {\n        Object.assign(thenable, data);\n        delete thenable.resolve;\n        delete thenable.reject;\n    }\n    thenable.resolve = (value)=>{\n        finalize({\n            status: \"fulfilled\",\n            value\n        });\n        resolve(value);\n    };\n    thenable.reject = (reason)=>{\n        finalize({\n            status: \"rejected\",\n            reason\n        });\n        reject(reason);\n    };\n    return thenable;\n}\nfunction tryResolveSync(promise) {\n    let data;\n    promise.then((result)=>{\n        data = result;\n        return result;\n    }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n    if (data !== void 0) {\n        return {\n            data\n        };\n    }\n    return void 0;\n}\n //# sourceMappingURL=thenable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer =  true || 0;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n    return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n    return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n    return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n    return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n    return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n    const { type = \"all\", exact, fetchStatus, predicate, queryKey, stale } = filters;\n    if (queryKey) {\n        if (exact) {\n            if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n                return false;\n            }\n        } else if (!partialMatchKey(query.queryKey, queryKey)) {\n            return false;\n        }\n    }\n    if (type !== \"all\") {\n        const isActive = query.isActive();\n        if (type === \"active\" && !isActive) {\n            return false;\n        }\n        if (type === \"inactive\" && isActive) {\n            return false;\n        }\n    }\n    if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n        return false;\n    }\n    if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n        return false;\n    }\n    if (predicate && !predicate(query)) {\n        return false;\n    }\n    return true;\n}\nfunction matchMutation(filters, mutation) {\n    const { exact, status, predicate, mutationKey } = filters;\n    if (mutationKey) {\n        if (!mutation.options.mutationKey) {\n            return false;\n        }\n        if (exact) {\n            if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n                return false;\n            }\n        } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n            return false;\n        }\n    }\n    if (status && mutation.state.status !== status) {\n        return false;\n    }\n    if (predicate && !predicate(mutation)) {\n        return false;\n    }\n    return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n    const hashFn = options?.queryKeyHashFn || hashKey;\n    return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n    return JSON.stringify(queryKey, (_, val)=>isPlainObject(val) ? Object.keys(val).sort().reduce((result, key)=>{\n            result[key] = val[key];\n            return result;\n        }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n        return Object.keys(b).every((key)=>partialMatchKey(a[key], b[key]));\n    }\n    return false;\n}\nfunction replaceEqualDeep(a, b) {\n    if (a === b) {\n        return a;\n    }\n    const array = isPlainArray(a) && isPlainArray(b);\n    if (array || isPlainObject(a) && isPlainObject(b)) {\n        const aItems = array ? a : Object.keys(a);\n        const aSize = aItems.length;\n        const bItems = array ? b : Object.keys(b);\n        const bSize = bItems.length;\n        const copy = array ? [] : {};\n        const aItemsSet = new Set(aItems);\n        let equalItems = 0;\n        for(let i = 0; i < bSize; i++){\n            const key = array ? i : bItems[i];\n            if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n                copy[key] = void 0;\n                equalItems++;\n            } else {\n                copy[key] = replaceEqualDeep(a[key], b[key]);\n                if (copy[key] === a[key] && a[key] !== void 0) {\n                    equalItems++;\n                }\n            }\n        }\n        return aSize === bSize && equalItems === aSize ? a : copy;\n    }\n    return b;\n}\nfunction shallowEqualObjects(a, b) {\n    if (!b || Object.keys(a).length !== Object.keys(b).length) {\n        return false;\n    }\n    for(const key in a){\n        if (a[key] !== b[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isPlainArray(value) {\n    return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n    if (!hasObjectPrototype(o)) {\n        return false;\n    }\n    const ctor = o.constructor;\n    if (ctor === void 0) {\n        return true;\n    }\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot)) {\n        return false;\n    }\n    if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n        return false;\n    }\n    if (Object.getPrototypeOf(o) !== Object.prototype) {\n        return false;\n    }\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n    return new Promise((resolve)=>{\n        setTimeout(resolve, timeout);\n    });\n}\nfunction replaceData(prevData, data, options) {\n    if (typeof options.structuralSharing === \"function\") {\n        return options.structuralSharing(prevData, data);\n    } else if (options.structuralSharing !== false) {\n        if (true) {\n            try {\n                return replaceEqualDeep(prevData, data);\n            } catch (error) {\n                console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n                throw error;\n            }\n        }\n        return replaceEqualDeep(prevData, data);\n    }\n    return data;\n}\nfunction keepPreviousData(previousData) {\n    return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n    const newItems = [\n        ...items,\n        item\n    ];\n    return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n    const newItems = [\n        item,\n        ...items\n    ];\n    return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n    if (true) {\n        if (options.queryFn === skipToken) {\n            console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n        }\n    }\n    if (!options.queryFn && fetchOptions?.initialPromise) {\n        return ()=>fetchOptions.initialPromise;\n    }\n    if (!options.queryFn || options.queryFn === skipToken) {\n        return ()=>Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n    }\n    return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n    if (typeof throwOnError === \"function\") {\n        return throwOnError(...params);\n    }\n    return !!throwOnError;\n}\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/IsRestoringProvider.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=IsRestoringProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9Jc1Jlc3RvcmluZ1Byb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozt3RkFFQSw2QkFBNkI7QUFDRTtBQUMvQixJQUFJQyxtQ0FBcUJELGdEQUFtQixDQUFDO0FBQzdDLElBQUlHLGlCQUFpQixJQUFNSCw2Q0FBZ0IsQ0FBQ0M7QUFDNUMsSUFBSUksc0JBQXNCSixtQkFBbUJLLFFBQVE7QUFJbkQsQ0FDRiwrQ0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZWFzZS1hZG1pbi1wYW5lbC8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL0lzUmVzdG9yaW5nUHJvdmlkZXIuanM/Y2E2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL0lzUmVzdG9yaW5nUHJvdmlkZXIudHNcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIElzUmVzdG9yaW5nQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZmFsc2UpO1xudmFyIHVzZUlzUmVzdG9yaW5nID0gKCkgPT4gUmVhY3QudXNlQ29udGV4dChJc1Jlc3RvcmluZ0NvbnRleHQpO1xudmFyIElzUmVzdG9yaW5nUHJvdmlkZXIgPSBJc1Jlc3RvcmluZ0NvbnRleHQuUHJvdmlkZXI7XG5leHBvcnQge1xuICBJc1Jlc3RvcmluZ1Byb3ZpZGVyLFxuICB1c2VJc1Jlc3RvcmluZ1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUlzUmVzdG9yaW5nUHJvdmlkZXIuanMubWFwIl0sIm5hbWVzIjpbIlJlYWN0IiwiSXNSZXN0b3JpbmdDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInVzZUlzUmVzdG9yaW5nIiwidXNlQ29udGV4dCIsIklzUmVzdG9yaW5nUHJvdmlkZXIiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>createValue());\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        errorResetBoundary.clearReset();\n    }, [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query, suspense })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]));\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query)=>query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions)=>{\n    if (defaultedOptions.suspense) {\n        const clamp = (value)=>value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n        const originalStaleTime = defaultedOptions.staleTime;\n        defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args)=>clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n        if (typeof defaultedOptions.gcTime === \"number\") {\n            defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n        }\n    }\n};\nvar willFetch = (result, isRestoring)=>result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result)=>defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary)=>observer.fetchOptimistic(defaultedOptions).catch(()=>{\n        errorResetBoundary.clearReset();\n    });\n //# sourceMappingURL=suspense.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(queryClient);\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new Observer(client, defaultedOptions));\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>{\n        const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop;\n        observer.updateResult();\n        return unsubscribe;\n    }, [\n        observer,\n        shouldSubscribe\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(defaultedOptions);\n    }, [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : (// subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise);\n        promise?.catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VRdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OERBRUEsa0JBQWtCO0FBQ21DO0FBQ0o7QUFDakQsU0FBU0UsU0FBU0MsT0FBTyxFQUFFQyxXQUFXO0lBQ3BDLE9BQU9ILDhEQUFZQSxDQUFDRSxTQUFTSCwrREFBYUEsRUFBRUk7QUFDOUM7QUFHRSxDQUNGLG9DQUFvQyIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlYXNlLWFkbWluLXBhbmVsLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vdXNlUXVlcnkuanM/Y2Q0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL3VzZVF1ZXJ5LnRzXG5pbXBvcnQgeyBRdWVyeU9ic2VydmVyIH0gZnJvbSBcIkB0YW5zdGFjay9xdWVyeS1jb3JlXCI7XG5pbXBvcnQgeyB1c2VCYXNlUXVlcnkgfSBmcm9tIFwiLi91c2VCYXNlUXVlcnkuanNcIjtcbmZ1bmN0aW9uIHVzZVF1ZXJ5KG9wdGlvbnMsIHF1ZXJ5Q2xpZW50KSB7XG4gIHJldHVybiB1c2VCYXNlUXVlcnkob3B0aW9ucywgUXVlcnlPYnNlcnZlciwgcXVlcnlDbGllbnQpO1xufVxuZXhwb3J0IHtcbiAgdXNlUXVlcnlcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VRdWVyeS5qcy5tYXAiXSwibmFtZXMiOlsiUXVlcnlPYnNlcnZlciIsInVzZUJhc2VRdWVyeSIsInVzZVF1ZXJ5Iiwib3B0aW9ucyIsInF1ZXJ5Q2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ })

};
;