package com.grocease.dto.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardOverviewDto {
    private BigDecimal todayRevenue;
    private BigDecimal monthlyRevenue;
    private Long todayOrders;
    private Long monthlyOrders;
    private Long totalUsers;
    private Long activeUsers;
    private BigDecimal averageOrderValue;
    private Map<String, Long> orderStatusDistribution;
    private BigDecimal revenueGrowthPercentage;
    private Long orderGrowthPercentage;
}
