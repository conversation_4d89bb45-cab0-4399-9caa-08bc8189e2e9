package com.grocease.dto.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SalesDataDto {
    private LocalDate date;
    private String period; // "2024-01" for monthly, "2024-W01" for weekly
    private BigDecimal totalRevenue;
    private Long orderCount;
    private BigDecimal averageOrderValue;
    private Long totalItemsSold;
}
