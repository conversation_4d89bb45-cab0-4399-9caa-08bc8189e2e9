package com.grocease.repository;

import com.grocease.dto.analytics.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AnalyticsRepository extends JpaRepository<Object, Long> {

    // Monthly Sales Data
    @Query("""
        SELECT new com.grocease.dto.analytics.SalesDataDto(
            CAST(o.createdAt AS LocalDate),
            CONCAT(YEAR(o.createdAt), '-', LPAD(MONTH(o.createdAt), 2, '0')),
            SUM(o.total),
            COUNT(o.id),
            AVG(o.total),
            SUM(oi.quantity)
        )
        FROM Order o
        LEFT JOIN o.items oi
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        GROUP BY YEAR(o.createdAt), MONTH(o.createdAt)
        ORDER BY YEAR(o.createdAt), MONTH(o.createdAt)
    """)
    List<SalesDataDto> getMonthlySalesData(@Param("startDate") LocalDateTime startDate, 
                                          @Param("endDate") LocalDateTime endDate);

    // Weekly Sales Data
    @Query("""
        SELECT new com.grocease.dto.analytics.SalesDataDto(
            CAST(o.createdAt AS LocalDate),
            CONCAT(YEAR(o.createdAt), '-W', LPAD(WEEK(o.createdAt), 2, '0')),
            SUM(o.total),
            COUNT(o.id),
            AVG(o.total),
            SUM(oi.quantity)
        )
        FROM Order o
        LEFT JOIN o.items oi
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        GROUP BY YEAR(o.createdAt), WEEK(o.createdAt)
        ORDER BY YEAR(o.createdAt), WEEK(o.createdAt)
    """)
    List<SalesDataDto> getWeeklySalesData(@Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);

    // Most Sold Products
    @Query("""
        SELECT new com.grocease.dto.analytics.ProductSalesDto(
            p.id,
            p.name,
            p.image,
            c.name,
            SUM(oi.quantity),
            SUM(oi.totalPrice),
            COUNT(DISTINCT o.id),
            AVG(oi.unitPrice)
        )
        FROM OrderItem oi
        JOIN oi.product p
        JOIN p.category c
        JOIN oi.order o
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        GROUP BY p.id, p.name, p.image, c.name
        ORDER BY SUM(oi.quantity) DESC
    """)
    List<ProductSalesDto> getMostSoldProducts(@Param("startDate") LocalDateTime startDate, 
                                             @Param("endDate") LocalDateTime endDate, 
                                             Pageable pageable);

    // Popular Categories
    @Query("""
        SELECT new com.grocease.dto.analytics.CategorySalesDto(
            c.id,
            c.name,
            c.image,
            SUM(oi.totalPrice),
            COUNT(DISTINCT o.id),
            SUM(oi.quantity),
            COUNT(DISTINCT p.id),
            AVG(o.total)
        )
        FROM OrderItem oi
        JOIN oi.product p
        JOIN p.category c
        JOIN oi.order o
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        GROUP BY c.id, c.name, c.image
        ORDER BY SUM(oi.totalPrice) DESC
    """)
    List<CategorySalesDto> getPopularCategories(@Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate);

    // User Engagement Metrics
    @Query("""
        SELECT new com.grocease.dto.analytics.UserEngagementDto(
            CAST(:date AS LocalDate),
            :period,
            COUNT(CASE WHEN u.createdAt >= :startDate AND u.createdAt <= :endDate THEN 1 END),
            COUNT(CASE WHEN o.createdAt >= :startDate AND o.createdAt <= :endDate THEN DISTINCT u.id END),
            COUNT(u.id),
            0.0,
            COUNT(CASE WHEN o.createdAt >= :startDate AND o.createdAt <= :endDate 
                      AND EXISTS(SELECT 1 FROM Order o2 WHERE o2.user.id = u.id AND o2.createdAt < :startDate) 
                      THEN DISTINCT u.id END)
        )
        FROM User u
        LEFT JOIN u.orders o
        WHERE u.createdAt <= :endDate
    """)
    UserEngagementDto getUserEngagementData(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate,
                                           @Param("date") LocalDateTime date,
                                           @Param("period") String period);

    // Today's Revenue
    @Query("""
        SELECT COALESCE(SUM(o.total), 0)
        FROM Order o
        WHERE DATE(o.createdAt) = CURRENT_DATE
        AND o.status != 'CANCELLED'
    """)
    BigDecimal getTodayRevenue();

    // Monthly Revenue
    @Query("""
        SELECT COALESCE(SUM(o.total), 0)
        FROM Order o
        WHERE YEAR(o.createdAt) = YEAR(CURRENT_DATE)
        AND MONTH(o.createdAt) = MONTH(CURRENT_DATE)
        AND o.status != 'CANCELLED'
    """)
    BigDecimal getMonthlyRevenue();

    // Today's Orders Count
    @Query("""
        SELECT COUNT(o.id)
        FROM Order o
        WHERE DATE(o.createdAt) = CURRENT_DATE
        AND o.status != 'CANCELLED'
    """)
    Long getTodayOrdersCount();

    // Monthly Orders Count
    @Query("""
        SELECT COUNT(o.id)
        FROM Order o
        WHERE YEAR(o.createdAt) = YEAR(CURRENT_DATE)
        AND MONTH(o.createdAt) = MONTH(CURRENT_DATE)
        AND o.status != 'CANCELLED'
    """)
    Long getMonthlyOrdersCount();

    // Total Users Count
    @Query("SELECT COUNT(u.id) FROM User u WHERE u.isActive = true")
    Long getTotalUsersCount();

    // Active Users (users who placed orders in last 30 days)
    @Query("""
        SELECT COUNT(DISTINCT u.id)
        FROM User u
        JOIN u.orders o
        WHERE o.createdAt >= :thirtyDaysAgo
        AND u.isActive = true
    """)
    Long getActiveUsersCount(@Param("thirtyDaysAgo") LocalDateTime thirtyDaysAgo);

    // Average Order Value
    @Query("""
        SELECT COALESCE(AVG(o.total), 0)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    BigDecimal getAverageOrderValue();

    // Order Status Distribution
    @Query("""
        SELECT o.status, COUNT(o.id)
        FROM Order o
        WHERE o.createdAt >= :startDate
        GROUP BY o.status
    """)
    List<Object[]> getOrderStatusDistribution(@Param("startDate") LocalDateTime startDate);
}
