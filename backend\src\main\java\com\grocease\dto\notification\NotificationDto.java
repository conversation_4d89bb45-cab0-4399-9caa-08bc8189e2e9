package com.grocease.dto.notification;

import com.grocease.entity.NotificationHistory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationDto {
    private Long id;
    private String title;
    private String message;
    private NotificationHistory.NotificationType type;
    private NotificationHistory.NotificationStatus status;
    private Boolean isBroadcast;
    private LocalDateTime sentAt;
    private String errorMessage;
}
