'use client'

import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import AdminLayout from '@/components/layout/AdminLayout'
import StatsCard from '@/components/dashboard/StatsCard'
import SalesChart from '@/components/dashboard/SalesChart'
import OrderStatusChart from '@/components/dashboard/OrderStatusChart'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  DollarSign, 
  ShoppingCart, 
  Users, 
  TrendingUp,
  Package,
  Clock
} from 'lucide-react'
import apiClient from '@/lib/api'
import { DashboardOverview, SalesData } from '@/types'

export default function DashboardPage() {
  const { data: overview, isLoading: overviewLoading } = useQuery({
    queryKey: ['dashboard-overview'],
    queryFn: () => apiClient.getDashboardOverview(),
  })

  const { data: monthlySales, isLoading: salesLoading } = useQuery({
    queryKey: ['monthly-sales'],
    queryFn: () => apiClient.getMonthlySalesData(6), // Last 6 months
  })

  if (overviewLoading || salesLoading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome to your admin dashboard
            </p>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to your admin dashboard
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Today's Revenue"
            value={overview?.todayRevenue || 0}
            change={overview?.revenueGrowthPercentage}
            icon={DollarSign}
            format="currency"
          />
          <StatsCard
            title="Today's Orders"
            value={overview?.todayOrders || 0}
            change={overview?.orderGrowthPercentage}
            icon={ShoppingCart}
          />
          <StatsCard
            title="Total Users"
            value={overview?.totalUsers || 0}
            icon={Users}
          />
          <StatsCard
            title="Average Order Value"
            value={overview?.averageOrderValue || 0}
            icon={TrendingUp}
            format="currency"
          />
        </div>

        {/* Charts Row */}
        <div className="grid gap-6 md:grid-cols-2">
          {monthlySales && (
            <SalesChart
              data={monthlySales}
              title="Sales Trend"
              description="Revenue and order count over the last 6 months"
              type="line"
            />
          )}
          
          {overview?.orderStatusDistribution && (
            <OrderStatusChart data={overview.orderStatusDistribution} />
          )}
        </div>

        {/* Additional Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${overview?.monthlyRevenue?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                {overview?.revenueGrowthPercentage && overview.revenueGrowthPercentage > 0 ? '+' : ''}
                {overview?.revenueGrowthPercentage?.toFixed(1) || '0'}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Orders</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview?.monthlyOrders?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                {overview?.orderGrowthPercentage && overview.orderGrowthPercentage > 0 ? '+' : ''}
                {overview?.orderGrowthPercentage || '0'}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview?.activeUsers?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                Users active in last 30 days
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
