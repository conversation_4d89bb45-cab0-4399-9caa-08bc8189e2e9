# =============================================================================
# DEVELOPMENT PROFILE CONFIGURATION
# =============================================================================
# This configuration is used for local development with H2 in-memory database
# Activate with: --spring.profiles.active=dev
# =============================================================================

spring:
  # H2 In-Memory Database for Development
  datasource:
    url: jdbc:h2:mem:grocease_dev;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5

  # JPA Configuration for Development
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2 Console for Development
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

  # SQL Initialization for Development
  sql:
    init:
      mode: always
      data-locations: classpath:data-dev.sql

# Development Logging Configuration
logging:
  level:
    com.grocease: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development-specific settings
app:
  cors:
    allowed-origins: "http://localhost:3000,http://localhost:19006,http://localhost:8081"

# Management endpoints for development
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
