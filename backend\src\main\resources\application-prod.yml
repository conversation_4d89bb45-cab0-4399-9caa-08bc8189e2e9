# =============================================================================
# PRODUCTION PROFILE CONFIGURATION
# =============================================================================
# This configuration is used for production deployment
# Activate with: --spring.profiles.active=prod
# =============================================================================

spring:
  # Production Database Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        generate_statistics: false
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true

  # SQL Initialization for Production
  sql:
    init:
      mode: never # Never run SQL scripts in production

# Production Logging Configuration
logging:
  level:
    com.grocease: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    org.springframework.web: WARN
    org.springframework.boot: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/grocease/grocease-backend.log

# Production Security & Performance
server:
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: true

# Production Management endpoints (restricted)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  info:
    env:
      enabled: false
