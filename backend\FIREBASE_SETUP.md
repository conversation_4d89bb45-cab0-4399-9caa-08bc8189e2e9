# Firebase Cloud Messaging Setup Guide

This guide will help you set up Firebase Cloud Messaging (FCM) for push notifications in the GrocEase backend.

## Prerequisites

- Google account
- Firebase project
- Android/iOS app registered in Firebase (for mobile app)

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name (e.g., "grocease-app")
4. Enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Add Your App to Firebase

### For Android App:
1. Click "Add app" and select Android
2. Enter your Android package name (e.g., `com.grocease.app`)
3. Enter app nickname (optional)
4. Enter SHA-1 certificate fingerprint (for development, you can skip this)
5. Download `google-services.json`
6. Add the file to your Android app's `app/` directory

### For iOS App:
1. Click "Add app" and select iOS
2. Enter your iOS bundle ID (e.g., `com.grocease.app`)
3. Enter app nickname (optional)
4. Download `GoogleService-Info.plist`
5. Add the file to your iOS app's root directory

## Step 3: Generate Service Account Key

1. In Firebase Console, go to Project Settings (gear icon)
2. Click on "Service accounts" tab
3. Click "Generate new private key"
4. Download the JSON file
5. Rename it to `firebase-service-account.json`
6. Place it in `src/main/resources/` directory of your Spring Boot project

## Step 4: Configure Spring Boot Application

1. Add the Firebase configuration to `application.yml`:

```yaml
firebase:
  config:
    file: firebase-service-account.json
```

2. Make sure the Firebase service account JSON file is in your classpath

## Step 5: Test Push Notifications

### Register Device Token

```bash
curl -X POST http://localhost:8080/api/notifications/register-token \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deviceToken=YOUR_DEVICE_TOKEN&deviceType=Android&deviceId=device123"
```

### Send Test Notification (Admin)

```bash
curl -X POST http://localhost:8080/api/admin/notifications/send \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Notification",
    "message": "This is a test message",
    "type": "GENERAL",
    "userIds": [1],
    "isBroadcast": false
  }'
```

## Step 6: Mobile App Integration

### React Native Setup

1. Install Firebase packages:
```bash
npm install @react-native-firebase/app @react-native-firebase/messaging
```

2. Configure Firebase in your React Native app:

```javascript
import messaging from '@react-native-firebase/messaging';

// Request permission (iOS)
const requestUserPermission = async () => {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    console.log('Authorization status:', authStatus);
  }
};

// Get FCM token
const getFCMToken = async () => {
  const fcmToken = await messaging().getToken();
  if (fcmToken) {
    console.log('FCM Token:', fcmToken);
    // Send this token to your backend
    registerDeviceToken(fcmToken);
  }
};

// Handle foreground messages
messaging().onMessage(async remoteMessage => {
  console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
});

// Handle background messages
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
});
```

## Step 7: Notification Types and Automation

The backend automatically sends notifications for:

- **Order Status Changes**: When order status is updated
- **Birthday Wishes**: Daily at 9 AM for users with birthdays
- **Promotional Offers**: Weekly on Mondays at 10 AM
- **New Products**: When new products are added
- **Low Stock Alerts**: For wishlisted items
- **Cart Reminders**: Daily at 6 PM for abandoned carts

## Step 8: Rate Limiting

The system includes rate limiting:
- **User Notifications**: 10 per hour per user
- **Broadcast Notifications**: 5 per hour globally
- **API Requests**: 100 per minute per client

## Troubleshooting

### Common Issues:

1. **Firebase not initialized**
   - Ensure `firebase-service-account.json` is in the classpath
   - Check file permissions and format

2. **Invalid device token**
   - Ensure the token is current and valid
   - Tokens can expire, implement token refresh

3. **Notification not received**
   - Check device token registration
   - Verify app is in foreground/background
   - Check notification permissions

4. **Rate limiting**
   - Check rate limit headers in API responses
   - Implement exponential backoff

### Logs to Check:

```bash
# Check application logs
tail -f logs/grocease-backend.log | grep -i notification

# Check Firebase connection
tail -f logs/grocease-backend.log | grep -i firebase
```

## Security Considerations

1. **Service Account Key**: Keep the Firebase service account key secure
2. **Device Tokens**: Validate and sanitize device tokens
3. **Rate Limiting**: Monitor for abuse and adjust limits as needed
4. **User Permissions**: Ensure users can opt-out of notifications

## Production Deployment

1. Use environment variables for sensitive configuration
2. Set up monitoring for notification delivery rates
3. Implement retry mechanisms for failed notifications
4. Consider using Firebase Admin SDK batch sending for large broadcasts
5. Set up alerts for high error rates

## Testing

Use Firebase Console's "Cloud Messaging" section to send test messages and verify your setup is working correctly.
