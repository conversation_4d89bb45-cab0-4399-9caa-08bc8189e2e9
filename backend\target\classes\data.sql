-- Insert Categories
INSERT INTO categories (name, image, icon, color, is_active, sort_order, created_at, updated_at, version) VALUES
('Fruits & Vegetables', 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=400', '🥕', '#4CAF50', true, 1, NOW(), NOW(), 0),
('Dairy & Eggs', 'https://images.unsplash.com/photo-**********-e9143da7973b?w=400', '🥛', '#2196F3', true, 2, NOW(), NOW(), 0),
('Meat & Seafood', 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400', '🥩', '#FF5722', true, 3, NOW(), NOW(), 0),
('Bakery', 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400', '🍞', '#FF9800', true, 4, NOW(), NOW(), 0),
('Pantry Staples', 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400', '🥫', '#795548', true, 5, NOW(), NOW(), 0),
('Beverages', 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400', '🥤', '#9C27B0', true, 6, NOW(), NOW(), 0),
('Snacks', 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=400', '🍿', '#FFC107', true, 7, NOW(), NOW(), 0),
('Frozen Foods', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400', '🧊', '#00BCD4', true, 8, NOW(), NOW(), 0);

-- Insert Products for Fruits & Vegetables
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Fresh Bananas', 'Sweet and ripe bananas, perfect for snacking or smoothies', 2.99, 3.49, 0.50, 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400', 'per bunch', true, 4.5, 128, 50, true, true, 1, NOW(), NOW(), 0),
('Organic Apples', 'Crisp and juicy organic red apples', 4.99, 5.99, 1.00, 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400', 'per lb', true, 4.7, 95, 30, true, true, 1, NOW(), NOW(), 0),
('Fresh Spinach', 'Nutrient-rich fresh spinach leaves', 3.49, null, null, 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400', 'per bunch', true, 4.3, 67, 25, true, false, 1, NOW(), NOW(), 0),
('Carrots', 'Fresh orange carrots, great for cooking or snacking', 2.49, null, null, 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?w=400', 'per lb', true, 4.4, 89, 40, true, false, 1, NOW(), NOW(), 0),
('Avocados', 'Ripe and creamy avocados, perfect for toast or salads', 5.99, 6.99, 1.00, 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400', 'per pack of 4', true, 4.6, 156, 20, true, true, 1, NOW(), NOW(), 0);

-- Insert Products for Dairy & Eggs
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Whole Milk', 'Fresh whole milk, rich in calcium and protein', 3.99, null, null, 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400', 'per gallon', true, 4.2, 234, 15, true, false, 2, NOW(), NOW(), 0),
('Free-Range Eggs', 'Farm-fresh free-range eggs, dozen pack', 4.49, 4.99, 0.50, 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400', 'per dozen', true, 4.8, 189, 25, true, true, 2, NOW(), NOW(), 0),
('Greek Yogurt', 'Creamy Greek yogurt, high in protein', 5.99, null, null, 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400', 'per container', true, 4.5, 145, 30, true, false, 2, NOW(), NOW(), 0),
('Cheddar Cheese', 'Sharp cheddar cheese, perfect for sandwiches', 6.99, 7.99, 1.00, 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400', 'per block', true, 4.4, 98, 18, true, false, 2, NOW(), NOW(), 0);

-- Insert Products for Meat & Seafood
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Chicken Breast', 'Boneless, skinless chicken breast, lean protein', 8.99, 9.99, 1.00, 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400', 'per lb', true, 4.6, 167, 12, true, true, 3, NOW(), NOW(), 0),
('Ground Beef', 'Fresh ground beef, 85% lean', 7.99, null, null, 'https://images.unsplash.com/photo-1588347818133-6b2e6d8b1c4e?w=400', 'per lb', true, 4.3, 134, 15, true, false, 3, NOW(), NOW(), 0),
('Salmon Fillet', 'Fresh Atlantic salmon fillet, rich in omega-3', 12.99, 14.99, 2.00, 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400', 'per lb', true, 4.7, 89, 8, true, true, 3, NOW(), NOW(), 0);

-- Insert Products for Bakery
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Whole Wheat Bread', 'Fresh baked whole wheat bread loaf', 3.49, null, null, 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400', 'per loaf', true, 4.4, 156, 20, true, false, 4, NOW(), NOW(), 0),
('Croissants', 'Buttery, flaky croissants, pack of 6', 5.99, 6.99, 1.00, 'https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=400', 'per pack', true, 4.6, 78, 12, true, false, 4, NOW(), NOW(), 0);

-- Insert Products for Pantry Staples
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Jasmine Rice', 'Premium jasmine rice, 5lb bag', 6.99, 7.99, 1.00, 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400', 'per 5lb bag', true, 4.5, 234, 25, true, true, 5, NOW(), NOW(), 0),
('Olive Oil', 'Extra virgin olive oil, cold pressed', 8.99, null, null, 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400', 'per bottle', true, 4.7, 189, 18, true, false, 5, NOW(), NOW(), 0),
('Pasta', 'Italian durum wheat pasta, spaghetti', 2.99, null, null, 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=400', 'per box', true, 4.3, 145, 35, true, false, 5, NOW(), NOW(), 0);

-- Insert Products for Beverages
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Orange Juice', 'Fresh squeezed orange juice, no pulp', 4.99, 5.99, 1.00, 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400', 'per bottle', true, 4.4, 167, 22, true, false, 6, NOW(), NOW(), 0),
('Sparkling Water', 'Natural sparkling water, 12-pack', 7.99, null, null, 'https://images.unsplash.com/photo-1523362628745-0c100150b504?w=400', 'per 12-pack', true, 4.2, 98, 30, true, false, 6, NOW(), NOW(), 0);

-- Insert Products for Snacks
INSERT INTO products (name, description, price, original_price, discount, image, unit, in_stock, rating, review_count, stock_quantity, is_active, is_featured, category_id, created_at, updated_at, version) VALUES
('Mixed Nuts', 'Premium mixed nuts, lightly salted', 8.99, 9.99, 1.00, 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=400', 'per container', true, 4.6, 134, 15, true, true, 7, NOW(), NOW(), 0),
('Potato Chips', 'Crispy potato chips, original flavor', 3.99, null, null, 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400', 'per bag', true, 4.1, 89, 40, true, false, 7, NOW(), NOW(), 0);

-- Insert Product Tags
INSERT INTO product_tags (name, product_id, created_at, updated_at, version) VALUES
('organic', 2, NOW(), NOW(), 0),
('fresh', 1, NOW(), NOW(), 0),
('healthy', 3, NOW(), NOW(), 0),
('vitamin-rich', 2, NOW(), NOW(), 0),
('protein', 6, NOW(), NOW(), 0),
('calcium', 5, NOW(), NOW(), 0),
('omega-3', 9, NOW(), NOW(), 0),
('whole-grain', 10, NOW(), NOW(), 0),
('gluten-free', 13, NOW(), NOW(), 0),
('natural', 16, NOW(), NOW(), 0);

-- Insert Banners
INSERT INTO banners (title, subtitle, image, background_color, text_color, action_text, action_url, is_active, sort_order, created_at, updated_at, version) VALUES
('Fresh Produce Sale', 'Up to 30% off on fresh fruits and vegetables', 'https://images.unsplash.com/photo-**********-92c53300491e?w=800', '#4CAF50', '#FFFFFF', 'Shop Now', '/products?category=1', true, 1, NOW(), NOW(), 0),
('Dairy Deals', 'Save big on milk, eggs, and cheese', 'https://images.unsplash.com/photo-**********-e9143da7973b?w=800', '#2196F3', '#FFFFFF', 'View Offers', '/products?category=2', true, 2, NOW(), NOW(), 0),
('Premium Meat & Seafood', 'Fresh cuts and catches delivered daily', 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=800', '#FF5722', '#FFFFFF', 'Explore', '/products?category=3', true, 3, NOW(), NOW(), 0),
('Pantry Essentials', 'Stock up on cooking essentials', 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800', '#795548', '#FFFFFF', 'Shop Pantry', '/products?category=5', true, 4, NOW(), NOW(), 0);
