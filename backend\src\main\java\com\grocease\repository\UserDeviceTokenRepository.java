package com.grocease.repository;

import com.grocease.entity.UserDeviceToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserDeviceTokenRepository extends JpaRepository<UserDeviceToken, Long> {
    
    List<UserDeviceToken> findByUserIdAndIsActiveTrue(Long userId);
    
    List<UserDeviceToken> findByIsActiveTrue();
    
    Optional<UserDeviceToken> findByDeviceToken(String deviceToken);
    
    @Modifying
    @Query("UPDATE UserDeviceToken u SET u.isActive = false WHERE u.user.id = :userId AND u.deviceToken != :currentToken")
    void deactivateOtherTokensForUser(@Param("userId") Long userId, @Param("currentToken") String currentToken);
    
    @Modifying
    @Query("UPDATE UserDeviceToken u SET u.isActive = false WHERE u.deviceToken = :deviceToken")
    void deactivateToken(@Param("deviceToken") String deviceToken);
}
