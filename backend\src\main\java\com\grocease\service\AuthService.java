package com.grocease.service;

import com.grocease.dto.auth.*;
import com.grocease.entity.OtpToken;
import com.grocease.entity.User;
import com.grocease.exception.BadRequestException;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.OtpTokenRepository;
import com.grocease.repository.UserRepository;
import com.grocease.util.DtoMapper;
import com.grocease.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Random;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserRepository userRepository;
    private final OtpTokenRepository otpTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final AuthenticationManager authenticationManager;
    private final DtoMapper dtoMapper;
    private final EmailService emailService;

    @Transactional
    public AuthResponse register(RegisterRequest request) {
        // Validate passwords match
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BadRequestException("Passwords do not match");
        }

        // Check if user already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BadRequestException("User already exists with email: " + request.getEmail());
        }

        if (userRepository.existsByPhone(request.getPhone())) {
            throw new BadRequestException("User already exists with phone: " + request.getPhone());
        }

        // Create new user
        User user = User.builder()
                .name(request.getName())
                .email(request.getEmail())
                .phone(request.getPhone())
                .password(passwordEncoder.encode(request.getPassword()))
                .isEmailVerified(false)
                .isPhoneVerified(false)
                .isActive(true)
                .role(User.Role.USER)
                .build();

        User savedUser = userRepository.save(user);

        // Generate email verification OTP
        generateAndSendOtp(savedUser, OtpToken.OtpType.EMAIL_VERIFICATION);

        // Generate tokens
        String token = jwtUtil.generateToken(savedUser);
        String refreshToken = jwtUtil.generateRefreshToken(savedUser);

        return AuthResponse.builder()
                .user(dtoMapper.toUserDto(savedUser))
                .token(token)
                .refreshToken(refreshToken)
                .expiresIn(86400000L) // 24 hours
                .build();
    }

    public AuthResponse login(LoginRequest request) {
        // Authenticate user
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
        );

        User user = (User) authentication.getPrincipal();

        // Generate tokens
        String token = jwtUtil.generateToken(user);
        String refreshToken = jwtUtil.generateRefreshToken(user);

        return AuthResponse.builder()
                .user(dtoMapper.toUserDto(user))
                .token(token)
                .refreshToken(refreshToken)
                .expiresIn(86400000L) // 24 hours
                .build();
    }

    @Transactional
    public void forgotPassword(ForgotPasswordRequest request) {
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + request.getEmail()));

        generateAndSendOtp(user, OtpToken.OtpType.PASSWORD_RESET);
    }

    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        // Validate passwords match
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BadRequestException("Passwords do not match");
        }

        // Validate OTP token
        OtpToken otpToken = otpTokenRepository.findByTokenAndTypeAndIsUsedFalse(
                        request.getToken(), OtpToken.OtpType.PASSWORD_RESET)
                .orElseThrow(() -> new BadRequestException("Invalid or expired reset token"));

        if (otpToken.isExpired()) {
            throw new BadRequestException("Reset token has expired");
        }

        // Update password
        User user = otpToken.getUser();
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        userRepository.save(user);

        // Mark token as used
        otpToken.setIsUsed(true);
        otpTokenRepository.save(otpToken);
    }

    @Transactional
    public void verifyOtp(OtpVerificationRequest request) {
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + request.getEmail()));

        OtpToken otpToken = otpTokenRepository.findByTokenAndTypeAndIsUsedFalse(
                        request.getOtp(), request.getType())
                .orElseThrow(() -> new BadRequestException("Invalid or expired OTP"));

        if (otpToken.isExpired()) {
            throw new BadRequestException("OTP has expired");
        }

        if (!otpToken.getUser().getId().equals(user.getId())) {
            throw new BadRequestException("OTP does not belong to this user");
        }

        // Mark OTP as used
        otpToken.setIsUsed(true);
        otpTokenRepository.save(otpToken);

        // Update user verification status
        if (request.getType() == OtpToken.OtpType.EMAIL_VERIFICATION) {
            user.setIsEmailVerified(true);
            userRepository.save(user);
        }
    }

    @Transactional
    public void resendOtp(String email, OtpToken.OtpType type) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

        // Check if there's already a valid OTP
        if (otpTokenRepository.existsByUserIdAndTypeAndIsUsedFalseAndExpiresAtAfter(
                user.getId(), type, LocalDateTime.now())) {
            throw new BadRequestException("An OTP is already active. Please wait before requesting a new one.");
        }

        generateAndSendOtp(user, type);
    }

    private void generateAndSendOtp(User user, OtpToken.OtpType type) {
        // Generate 6-digit OTP
        String otp = String.format("%06d", new Random().nextInt(999999));

        // Create OTP token
        OtpToken otpToken = OtpToken.builder()
                .token(otp)
                .type(type)
                .expiresAt(LocalDateTime.now().plusMinutes(10)) // 10 minutes expiry
                .isUsed(false)
                .user(user)
                .build();

        otpTokenRepository.save(otpToken);

        // Send email
        String subject = getOtpEmailSubject(type);
        String message = getOtpEmailMessage(otp, type);
        emailService.sendEmail(user.getEmail(), subject, message);

        log.info("OTP sent to user: {} for type: {}", user.getEmail(), type);
    }

    private String getOtpEmailSubject(OtpToken.OtpType type) {
        return switch (type) {
            case EMAIL_VERIFICATION -> "Verify Your Email - GrocEase";
            case PASSWORD_RESET -> "Reset Your Password - GrocEase";
            case PHONE_VERIFICATION -> "Verify Your Phone - GrocEase";
        };
    }

    private String getOtpEmailMessage(String otp, OtpToken.OtpType type) {
        String action = switch (type) {
            case EMAIL_VERIFICATION -> "verify your email";
            case PASSWORD_RESET -> "reset your password";
            case PHONE_VERIFICATION -> "verify your phone number";
        };

        return String.format(
                "Your OTP to %s is: %s\n\nThis OTP will expire in 10 minutes.\n\nIf you didn't request this, please ignore this email.",
                action, otp
        );
    }
}
