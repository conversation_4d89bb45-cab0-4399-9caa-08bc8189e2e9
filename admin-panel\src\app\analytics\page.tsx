'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import AdminLayout from '@/components/layout/AdminLayout'
import SalesChart from '@/components/dashboard/SalesChart'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  Package, 
  Users, 
  ShoppingCart,
  Calendar,
  BarChart3
} from 'lucide-react'
import apiClient from '@/lib/api'
import { formatCurrency, formatNumber } from '@/lib/utils'
import Image from 'next/image'

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<'weekly' | 'monthly'>('monthly')

  const { data: salesData } = useQuery({
    queryKey: ['sales-data', timeRange],
    queryFn: () => timeRange === 'monthly' 
      ? apiClient.getMonthlySalesData(12)
      : apiClient.getWeeklySalesData(12),
  })

  const { data: topProducts } = useQuery({
    queryKey: ['top-products'],
    queryFn: () => apiClient.getMostSoldProducts(30, 10),
  })

  const { data: popularCategories } = useQuery({
    queryKey: ['popular-categories'],
    queryFn: () => apiClient.getPopularCategories(30),
  })

  const { data: userEngagement } = useQuery({
    queryKey: ['user-engagement'],
    queryFn: () => apiClient.getUserEngagementMetrics(30),
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
            <p className="text-muted-foreground">
              Detailed insights into your business performance
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={timeRange === 'weekly' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange('weekly')}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Weekly
            </Button>
            <Button
              variant={timeRange === 'monthly' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange('monthly')}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Monthly
            </Button>
          </div>
        </div>

        <Tabs defaultValue="sales" className="space-y-6">
          <TabsList>
            <TabsTrigger value="sales">Sales Analytics</TabsTrigger>
            <TabsTrigger value="products">Product Performance</TabsTrigger>
            <TabsTrigger value="users">User Engagement</TabsTrigger>
          </TabsList>

          <TabsContent value="sales" className="space-y-6">
            {/* Sales Chart */}
            {salesData && (
              <SalesChart
                data={salesData}
                title={`${timeRange === 'monthly' ? 'Monthly' : 'Weekly'} Sales Performance`}
                description="Revenue and order trends over time"
                type="line"
              />
            )}

            {/* Category Performance */}
            {popularCategories && (
              <Card>
                <CardHeader>
                  <CardTitle>Category Performance</CardTitle>
                  <CardDescription>Top performing categories by revenue</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {popularCategories.slice(0, 5).map((category, index) => (
                      <div key={category.categoryId} className="flex items-center space-x-4">
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                            {index + 1}
                          </div>
                          <div className="flex items-center space-x-3">
                            {category.categoryImage && (
                              <Image
                                src={category.categoryImage}
                                alt={category.categoryName}
                                width={40}
                                height={40}
                                className="rounded-lg object-cover"
                              />
                            )}
                            <div>
                              <p className="font-medium">{category.categoryName}</p>
                              <p className="text-sm text-muted-foreground">
                                {formatNumber(category.orderCount)} orders
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(category.totalRevenue)}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatNumber(category.totalItemsSold)} items sold
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="products" className="space-y-6">
            {/* Top Products */}
            {topProducts && (
              <Card>
                <CardHeader>
                  <CardTitle>Best Selling Products</CardTitle>
                  <CardDescription>Top 10 products by quantity sold</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {topProducts.map((product, index) => (
                      <div key={product.productId} className="flex items-center space-x-4">
                        <div className="flex items-center space-x-3 flex-1">
                          <Badge variant={index < 3 ? 'default' : 'secondary'}>
                            #{index + 1}
                          </Badge>
                          <div className="flex items-center space-x-3">
                            {product.productImage && (
                              <Image
                                src={product.productImage}
                                alt={product.productName}
                                width={50}
                                height={50}
                                className="rounded-lg object-cover"
                              />
                            )}
                            <div>
                              <p className="font-medium">{product.productName}</p>
                              <p className="text-sm text-muted-foreground">
                                {product.categoryName}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatNumber(product.quantitySold)} sold</p>
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(product.totalRevenue)} revenue
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            {/* User Engagement */}
            {userEngagement && userEngagement.length > 0 && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">New Users</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(userEngagement[0].newUsers)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Last 30 days
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(userEngagement[0].activeUsers)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Last 30 days
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {userEngagement[0].retentionRate.toFixed(1)}%
                    </div>
                    <p className="text-xs text-muted-foreground">
                      User retention
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(userEngagement[0].totalUsers)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      All time
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
