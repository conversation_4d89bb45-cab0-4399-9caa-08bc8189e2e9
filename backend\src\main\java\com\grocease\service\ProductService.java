package com.grocease.service;

import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.ProductDto;
import com.grocease.entity.Category;
import com.grocease.entity.Product;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.CategoryRepository;
import com.grocease.repository.ProductRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductService {

    private final ProductRepository productRepository;
    private final CategoryRepository categoryRepository;
    private final DtoMapper dtoMapper;

    public List<CategoryDto> getAllCategories() {
        List<Category> categories = categoryRepository.findAllActiveCategories();
        return dtoMapper.toCategoryDtoList(categories);
    }

    public PaginatedResponse<ProductDto> getProducts(Long categoryId, int page, int limit, String search, String sortBy, String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, limit, sort);

        Page<Product> productPage;

        if (search != null && !search.trim().isEmpty()) {
            productPage = productRepository.searchProducts(search.trim(), pageable);
        } else if (categoryId != null) {
            productPage = productRepository.findByCategoryIdWithCategory(categoryId, pageable);
        } else {
            productPage = productRepository.findAllActiveWithCategory(pageable);
        }

        List<ProductDto> productDtos = dtoMapper.toProductDtoList(productPage.getContent());

        return PaginatedResponse.<ProductDto>builder()
                .data(productDtos)
                .pagination(PaginatedResponse.PaginationInfo.builder()
                        .page(page)
                        .limit(limit)
                        .total(productPage.getTotalElements())
                        .totalPages(productPage.getTotalPages())
                        .build())
                .build();
    }

    public ProductDto getProductById(Long productId) {
        Product product = productRepository.findByIdWithDetails(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + productId));
        return dtoMapper.toProductDto(product);
    }

    public List<ProductDto> getFeaturedProducts() {
        List<Product> products = productRepository.findByIsFeaturedTrueAndIsActiveTrue();
        return dtoMapper.toProductDtoList(products);
    }

    public List<ProductDto> searchProducts(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        Pageable pageable = PageRequest.of(0, 20); // Limit search results
        Page<Product> productPage = productRepository.searchProducts(query.trim(), pageable);
        return dtoMapper.toProductDtoList(productPage.getContent());
    }

    public CategoryDto getCategoryById(Long categoryId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + categoryId));
        return dtoMapper.toCategoryDto(category);
    }
}
