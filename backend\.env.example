# =============================================================================
# GROCEASE BACKEND - ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env file to version control!
# =============================================================================

# =============================================================================
# SPRING CONFIGURATION
# =============================================================================
SPRING_PROFILES_ACTIVE=local
SERVER_PORT=8080

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Settings
DATABASE_URL=********************************************
DB_USERNAME=grocease_user
DB_PASSWORD=your_secure_password_here
DB_DRIVER=org.postgresql.Driver

# Database Connection Pool Settings (Optional - defaults provided)
DB_POOL_SIZE=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000

# =============================================================================
# JPA/HIBERNATE CONFIGURATION
# =============================================================================
# JPA Settings (Optional - defaults provided)
JPA_DDL_AUTO=update
JPA_SHOW_SQL=false
JPA_FORMAT_SQL=false
JPA_DIALECT=org.hibernate.dialect.PostgreSQLDialect

# SQL Initialization (Optional)
SQL_INIT_MODE=never
SQL_DATA_LOCATIONS=classpath:data.sql

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration (REQUIRED)
JWT_SECRET=your_super_secret_jwt_key_at_least_32_characters_long_for_security
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000
JWT_HEADER=Authorization
JWT_PREFIX=Bearer 

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email Settings for OTP and notifications (REQUIRED)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# =============================================================================
# CLOUDINARY CONFIGURATION
# =============================================================================
# Image Upload Service (REQUIRED for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
# Firebase for Push Notifications (OPTIONAL)
FIREBASE_CONFIG_FILE=firebase-service-account.json

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006,http://localhost:8081

# File Upload Settings
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=10MB

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging Levels (Optional - defaults provided)
LOG_LEVEL_APP=DEBUG
LOG_LEVEL_SECURITY=DEBUG
LOG_LEVEL_SQL=DEBUG
LOG_LEVEL_SQL_PARAMS=TRACE
LOG_FILE=logs/grocease-backend.log

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Management Endpoints (Optional)
MANAGEMENT_ENDPOINTS=health,info,metrics
HEALTH_SHOW_DETAILS=when-authorized
