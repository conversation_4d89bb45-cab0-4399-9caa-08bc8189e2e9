package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.analytics.*;
import com.grocease.service.AnalyticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analytics")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AnalyticsController {

    private final AnalyticsService analyticsService;

    @GetMapping("/sales/monthly")
    public ResponseEntity<ApiResponse<List<SalesDataDto>>> getMonthlySalesData(
            @RequestParam(defaultValue = "12") int months) {
        log.info("Getting monthly sales data for {} months", months);
        List<SalesDataDto> salesData = analyticsService.getMonthlySalesData(months);
        return ResponseEntity.ok(ApiResponse.success(salesData, "Monthly sales data retrieved successfully"));
    }

    @GetMapping("/sales/weekly")
    public ResponseEntity<ApiResponse<List<SalesDataDto>>> getWeeklySalesData(
            @RequestParam(defaultValue = "12") int weeks) {
        log.info("Getting weekly sales data for {} weeks", weeks);
        List<SalesDataDto> salesData = analyticsService.getWeeklySalesData(weeks);
        return ResponseEntity.ok(ApiResponse.success(salesData, "Weekly sales data retrieved successfully"));
    }

    @GetMapping("/products/most-sold")
    public ResponseEntity<ApiResponse<List<ProductSalesDto>>> getMostSoldProducts(
            @RequestParam(defaultValue = "30") int days,
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Getting most sold products for {} days, limit: {}", days, limit);
        List<ProductSalesDto> products = analyticsService.getMostSoldProducts(days, limit);
        return ResponseEntity.ok(ApiResponse.success(products, "Most sold products retrieved successfully"));
    }

    @GetMapping("/categories/popular")
    public ResponseEntity<ApiResponse<List<CategorySalesDto>>> getPopularCategories(
            @RequestParam(defaultValue = "30") int days) {
        log.info("Getting popular categories for {} days", days);
        List<CategorySalesDto> categories = analyticsService.getPopularCategories(days);
        return ResponseEntity.ok(ApiResponse.success(categories, "Popular categories retrieved successfully"));
    }

    @GetMapping("/users/engagement")
    public ResponseEntity<ApiResponse<List<UserEngagementDto>>> getUserEngagementMetrics(
            @RequestParam(defaultValue = "30") int days) {
        log.info("Getting user engagement metrics for {} days", days);
        List<UserEngagementDto> engagement = analyticsService.getUserEngagementMetrics(days);
        return ResponseEntity.ok(ApiResponse.success(engagement, "User engagement metrics retrieved successfully"));
    }
}
